#!/bin/bash

# Stop Real-time Crypto Monitoring System
# This script stops both the Spring Boot backend and Next.js frontend

set -e

echo "🛑 Stopping Real-time Crypto Monitoring System..."
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to stop a service by PID file
stop_service() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            print_status "Stopping $service_name (PID: $pid)..."
            kill $pid
            
            # Wait for process to stop
            local count=0
            while ps -p $pid > /dev/null 2>&1 && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            if ps -p $pid > /dev/null 2>&1; then
                print_warning "$service_name didn't stop gracefully, forcing termination..."
                kill -9 $pid
            fi
            
            print_success "$service_name stopped successfully"
        else
            print_warning "$service_name was not running (PID: $pid)"
        fi
        rm -f "$pid_file"
    else
        print_warning "$service_name PID file not found"
    fi
}

# Function to stop services by port
stop_by_port() {
    local port=$1
    local service_name=$2
    
    local pid=$(lsof -ti:$port 2>/dev/null || true)
    if [ -n "$pid" ]; then
        print_status "Found $service_name running on port $port (PID: $pid)"
        kill $pid
        print_success "$service_name on port $port stopped"
    else
        print_status "No process found running on port $port"
    fi
}

# Main cleanup function
cleanup() {
    print_status "Stopping services..."
    
    # Stop backend
    stop_service "Backend" "backend.pid"
    
    # Stop frontend
    stop_service "Frontend" "frontend.pid"
    
    # Additional cleanup - stop any remaining processes on known ports
    print_status "Checking for remaining processes on known ports..."
    stop_by_port 8080 "Backend"
    stop_by_port 3000 "Frontend"
    
    # Clean up log files (optional)
    if [ "$1" = "--clean-logs" ]; then
        print_status "Cleaning up log files..."
        rm -f backend.log frontend.log
        print_success "Log files cleaned"
    fi
    
    print_success "All services stopped successfully!"
}

# Show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --clean-logs    Remove log files after stopping services"
    echo "  --help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Stop services only"
    echo "  $0 --clean-logs       # Stop services and clean logs"
}

# Parse command line arguments
case "${1:-}" in
    --help)
        show_help
        exit 0
        ;;
    --clean-logs)
        cleanup --clean-logs
        ;;
    "")
        cleanup
        ;;
    *)
        print_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
