# WebSocket Backend Testing Guide

## Overview

This guide explains how to test the WebSocket functionality with both a mock backend and a real Spring Boot backend.

## Quick Start

### Option 1: Using Mock Backend (Recommended for Testing)

1. **Start the mock backend server:**
   ```bash
   npm run mock-backend
   ```

2. **Start the frontend (in another terminal):**
   ```bash
   npm run dev
   ```

3. **Or start both together:**
   ```bash
   npm run dev:full
   ```

### Option 2: Using Real Spring Boot Backend

1. **Start your Spring Boot backend server on port 8080**
2. **Start the frontend:**
   ```bash
   npm run dev
   ```

## Testing WebSocket Connection

### 1. WebSocket Test Page

Visit: `http://localhost:3000/websocket-test`

**Features:**
- ✅ Backend health check status
- 🔌 Manual WebSocket connection control
- 📊 Real-time connection statistics
- 📝 Test message sending
- 🐛 Detailed error logging

**Steps to test:**
1. Check that "Backend Health" shows "✅ Healthy"
2. Click "Connect" to establish WebSocket connection
3. Monitor connection status and statistics
4. Send test messages when connected
5. Check browser console for detailed logs

### 2. Crypto Monitoring Page

Visit: `http://localhost:3000/crypto`

**Features:**
- 📈 Real-time crypto price updates (mock data)
- 🔔 Price alerts and notifications
- 📊 Connection status monitoring
- 🎛️ Manual connection controls

**Steps to test:**
1. Click "Connect" button in the connection status panel
2. Monitor WebSocket connection status
3. Observe mock price updates (if using mock backend)
4. Test subscription management

## Mock Backend Features

The mock backend (`scripts/mock-backend.js`) provides:

### HTTP Endpoints
- `GET /api/v1/test/health` - Health check endpoint
- `GET /api/v1/test/public` - Public test endpoint
- `GET /api/v1/test/protected` - Protected test endpoint

### WebSocket Features
- **Connection handling** with detailed logging
- **Message echoing** - sends back received messages
- **Mock crypto data** - simulates price updates
- **Heartbeat messages** - every 30 seconds
- **Error handling** with proper error responses

### Sample WebSocket Messages

**Welcome message (on connection):**
```json
{
  "type": "WELCOME",
  "message": "Connected to mock backend WebSocket",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "data": { "connectionId": "abc123def" }
}
```

**Price update (crypto subscription):**
```json
{
  "type": "PRICE_UPDATE",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "data": {
    "symbol": "BTC",
    "name": "Bitcoin",
    "price_usd": 45123.45,
    "percent_change_24h": 2.34,
    "source": "mock-backend",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "last_updated": "2024-01-15T10:30:00.000Z"
  }
}
```

## Troubleshooting

### Backend Health Check Fails

**Symptoms:**
- Backend Health shows "❌ Unhealthy"
- WebSocket connection fails immediately
- Error: "Backend server is not available"

**Solutions:**
1. **Check if backend is running:**
   ```bash
   curl http://localhost:8080/api/v1/test/health
   ```

2. **Start mock backend:**
   ```bash
   npm run mock-backend
   ```

3. **Check port availability:**
   ```bash
   lsof -i :8080
   ```

4. **Verify firewall settings**

### WebSocket Connection Issues

**Symptoms:**
- Connection status shows "Error" or "Disconnected"
- STOMP errors in console
- Connection timeouts

**Solutions:**
1. **Check backend logs** for WebSocket errors
2. **Verify WebSocket endpoint** is accessible
3. **Check browser console** for detailed error messages
4. **Test with mock backend** to isolate issues

### STOMP Protocol Issues

**Common STOMP errors:**
- `STOMP Error: Connection failed` - Backend not reachable
- `STOMP Error: Authentication failed` - Check headers/credentials
- `STOMP Error: Subscription failed` - Invalid destination

**Debug steps:**
1. Enable debug mode in WebSocket config
2. Check STOMP frame details in console
3. Verify subscription destinations match backend
4. Test with simple echo messages first

## Configuration

### Environment Variables

```env
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1
NEXT_PUBLIC_WS_URL=http://localhost:8080/ws
NODE_ENV=development
```

### WebSocket Configuration

```typescript
// Default configuration
{
  url: 'http://localhost:8080/ws',
  debug: true,
  reconnectDelay: 5000,
  maxReconnectAttempts: 5,
  heartbeatIncoming: 10000,
  heartbeatOutgoing: 10000
}
```

## Integration with Real Backend

### Spring Boot WebSocket Configuration

Your Spring Boot backend should have:

1. **WebSocket configuration** with STOMP support
2. **Health check endpoint** at `/api/v1/test/health`
3. **CORS configuration** for `http://localhost:3000`
4. **Message brokers** for crypto price updates

### Expected Endpoints

```java
@RestController
@RequestMapping("/api/v1/test")
public class TestController {
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        // Return health status
    }
}

@Controller
public class WebSocketController {
    
    @MessageMapping("/crypto.subscribe")
    @SendTo("/topic/crypto.{symbol}")
    public void subscribeToCrypto(String symbol) {
        // Handle crypto subscription
    }
}
```

## Performance Tips

1. **Limit reconnection attempts** to avoid spam
2. **Use heartbeat messages** to detect connection issues
3. **Implement proper error handling** for network issues
4. **Monitor connection statistics** for debugging
5. **Use connection pooling** for multiple subscriptions

## Next Steps

1. Test with your real Spring Boot backend
2. Implement proper authentication if needed
3. Add more sophisticated error handling
4. Implement connection retry with exponential backoff
5. Add metrics and monitoring for production use
