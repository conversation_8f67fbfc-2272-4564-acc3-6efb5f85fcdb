#!/usr/bin/env node

/**
 * Mock Backend Server for WebSocket Testing
 * Simple Express server with WebSocket support for testing the frontend
 */

const express = require('express')
const http = require('http')
const WebSocket = require('ws')
const cors = require('cors')

const app = express()
const server = http.createServer(app)

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}))

app.use(express.json())

// Health check endpoint
app.get('/api/v1/test/health', (req, res) => {
  console.log('Health check requested')
  res.json({
    success: true,
    message: 'Backend server is healthy',
    timestamp: new Date().toISOString(),
    data: {
      status: 'UP',
      version: '1.0.0',
      uptime: process.uptime()
    }
  })
})

// Test endpoints
app.get('/api/v1/test/public', (req, res) => {
  res.json({
    success: true,
    message: 'Public endpoint working',
    timestamp: new Date().toISOString(),
    data: { message: 'Hello from mock backend!' }
  })
})

app.get('/api/v1/test/protected', (req, res) => {
  res.json({
    success: true,
    message: 'Protected endpoint working',
    timestamp: new Date().toISOString(),
    data: { message: 'Protected data accessed successfully' }
  })
})

// WebSocket server
const wss = new WebSocket.Server({ 
  server,
  path: '/ws'
})

console.log('WebSocket server created on path: /ws')

wss.on('connection', (ws, req) => {
  console.log('New WebSocket connection established')
  console.log('Connection details:', {
    url: req.url,
    headers: req.headers,
    remoteAddress: req.socket.remoteAddress
  })

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'WELCOME',
    message: 'Connected to mock backend WebSocket',
    timestamp: new Date().toISOString(),
    data: { connectionId: Math.random().toString(36).substr(2, 9) }
  }))

  // Handle incoming messages
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString())
      console.log('Received WebSocket message:', data)

      // Echo the message back
      ws.send(JSON.stringify({
        type: 'ECHO',
        message: 'Message received and processed',
        timestamp: new Date().toISOString(),
        data: data
      }))

      // Send a test crypto price update
      if (data.type === 'SUBSCRIBE_CRYPTO') {
        setTimeout(() => {
          ws.send(JSON.stringify({
            type: 'PRICE_UPDATE',
            timestamp: new Date().toISOString(),
            data: {
              symbol: 'BTC',
              name: 'Bitcoin',
              price_usd: 45000 + Math.random() * 1000,
              percent_change_24h: (Math.random() - 0.5) * 10,
              source: 'mock-backend',
              timestamp: new Date().toISOString(),
              last_updated: new Date().toISOString()
            }
          }))
        }, 1000)
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error)
      ws.send(JSON.stringify({
        type: 'ERROR',
        message: 'Invalid message format',
        timestamp: new Date().toISOString(),
        data: { error: error.message }
      }))
    }
  })

  // Handle connection close
  ws.on('close', (code, reason) => {
    console.log('WebSocket connection closed:', { code, reason: reason.toString() })
  })

  // Handle errors
  ws.on('error', (error) => {
    console.error('WebSocket error:', error)
  })

  // Send periodic heartbeat
  const heartbeat = setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'HEARTBEAT',
        timestamp: new Date().toISOString(),
        data: { status: 'alive' }
      }))
    } else {
      clearInterval(heartbeat)
    }
  }, 30000) // Every 30 seconds
})

// Start server
const PORT = process.env.PORT || 8080
server.listen(PORT, () => {
  console.log(`🚀 Mock backend server running on http://localhost:${PORT}`)
  console.log(`📡 WebSocket endpoint: ws://localhost:${PORT}/ws`)
  console.log(`🏥 Health check: http://localhost:${PORT}/api/v1/test/health`)
  console.log(`📝 Test endpoints:`)
  console.log(`   - GET http://localhost:${PORT}/api/v1/test/public`)
  console.log(`   - GET http://localhost:${PORT}/api/v1/test/protected`)
  console.log('')
  console.log('Press Ctrl+C to stop the server')
})

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down mock backend server...')
  server.close(() => {
    console.log('✅ Server closed successfully')
    process.exit(0)
  })
})

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...')
  server.close(() => {
    console.log('✅ Server closed successfully')
    process.exit(0)
  })
})
