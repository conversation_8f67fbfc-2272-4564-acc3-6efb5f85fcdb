{"name": "monitoring-tool", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:export": "node scripts/build-export.js", "start": "next start", "lint": "next lint", "mock-backend": "node scripts/mock-backend.js", "dev:full": "concurrently \"npm run mock-backend\" \"npm run dev\""}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stomp/stompjs": "^7.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "recharts": "^3.0.2", "sockjs-client": "^1.6.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.74"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^8.2.2", "cors": "^2.8.5", "eslint": "^9", "eslint-config-next": "15.3.5", "express": "^4.18.2", "tailwindcss": "^4", "typescript": "^5", "ws": "^8.14.2"}}