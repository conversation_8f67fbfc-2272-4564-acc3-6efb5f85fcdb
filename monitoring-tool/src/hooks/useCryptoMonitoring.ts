import { useState, useEffect, useCallback, useRef } from 'react'
import { useWebSocket, WebSocketMessage } from './useWebSocket'

/**
 * Crypto Monitoring Hook
 * High-level hook for cryptocurrency price monitoring and alerts
 * 
 * Features:
 * - Real-time price updates
 * - Multi-symbol tracking
 * - Price alerts and notifications
 * - Historical data management
 * - Performance optimization
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

export interface CryptoPriceData {
  symbol: string
  name: string
  price_usd: number
  price_btc?: number
  market_cap_usd?: number
  volume_24h_usd?: number
  percent_change_24h?: number
  percent_change_7d?: number
  percent_change_30d?: number
  rank?: number
  source: string
  timestamp: string
  last_updated: string
}

export interface CryptoAlert {
  id: string
  symbol: string
  message: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  timestamp: string
  data: CryptoPriceData
}

export interface MarketUpdate {
  type: string
  data: CryptoPriceData[]
  timestamp: string
}

export interface CryptoMonitoringState {
  // Price data
  prices: Record<string, CryptoPriceData>
  marketData: CryptoPriceData[]
  
  // Alerts and notifications
  alerts: CryptoAlert[]
  unreadAlertsCount: number
  
  // Connection and loading states
  isConnected: boolean
  isLoading: boolean
  error: string | null
  
  // Subscriptions
  subscribedSymbols: Set<string>
  
  // Statistics
  lastUpdate: Date | null
  updateCount: number
}

export interface CryptoMonitoringActions {
  // Symbol management
  subscribeToSymbol: (symbol: string) => void
  unsubscribeFromSymbol: (symbol: string) => void
  subscribeToMarket: () => void
  unsubscribeFromMarket: () => void
  
  // Alert management
  subscribeToAlerts: () => void
  markAlertAsRead: (alertId: string) => void
  clearAllAlerts: () => void
  
  // Connection management
  connect: () => void
  disconnect: () => void
  reconnect: () => void
  
  // Data management
  refreshData: () => void
  clearData: () => void

  // WebSocket messaging
  sendMessage: (destination: string, message: any, headers?: Record<string, string>) => void
}

export interface UseCryptoMonitoringReturn {
  state: CryptoMonitoringState
  actions: CryptoMonitoringActions
}

export const useCryptoMonitoring = (
  symbols: string[] = ['BTC', 'ETH', 'ADA'],
  autoConnect: boolean = true
): UseCryptoMonitoringReturn => {
  
  // WebSocket connection
  const webSocket = useWebSocket({
    url: process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:8080/ws',
    debug: process.env.NODE_ENV === 'development',
    headers: {
      'Authorization': `Bearer ${typeof window !== 'undefined' ? localStorage.getItem('authToken') || '' : ''}`,
      'X-User-Id': typeof window !== 'undefined' ? localStorage.getItem('userId') || 'anonymous' : 'anonymous'
    }
  })

  // State management
  const [state, setState] = useState<CryptoMonitoringState>({
    prices: {},
    marketData: [],
    alerts: [],
    unreadAlertsCount: 0,
    isConnected: false,
    isLoading: false,
    error: null,
    subscribedSymbols: new Set(),
    lastUpdate: null,
    updateCount: 0
  })

  // Subscription tracking
  const subscriptionIdsRef = useRef<Map<string, string>>(new Map())

  // Update connection state
  useEffect(() => {
    setState(prev => ({
      ...prev,
      isConnected: webSocket.isConnected,
      error: webSocket.error
    }))
  }, [webSocket.isConnected, webSocket.error])

  // Handle price updates
  const handlePriceUpdate = useCallback((message: WebSocketMessage) => {
    if (message.type === 'PRICE_UPDATE' && message.data) {
      const priceData: CryptoPriceData = message.data
      
      setState(prev => ({
        ...prev,
        prices: {
          ...prev.prices,
          [priceData.symbol]: priceData
        },
        lastUpdate: new Date(),
        updateCount: prev.updateCount + 1
      }))
    }
  }, [])

  // Handle market updates
  const handleMarketUpdate = useCallback((message: WebSocketMessage) => {
    if (message.type === 'MARKET_UPDATE' && message.data) {
      const marketData: CryptoPriceData[] = message.data
      
      setState(prev => {
        const newPrices = { ...prev.prices }
        marketData.forEach(data => {
          newPrices[data.symbol] = data
        })
        
        return {
          ...prev,
          prices: newPrices,
          marketData,
          lastUpdate: new Date(),
          updateCount: prev.updateCount + 1
        }
      })
    }
  }, [])

  // Handle crypto alerts
  const handleCryptoAlert = useCallback((message: WebSocketMessage) => {
    if (message.type === 'PRICE_ALERT' || message.type === 'CRYPTO_ALERT') {
      const alert: CryptoAlert = {
        id: message.messageId || `alert-${Date.now()}`,
        symbol: message.symbol || 'UNKNOWN',
        message: message.message || 'Price alert',
        severity: message.severity || 'MEDIUM',
        timestamp: message.timestamp,
        data: message.data
      }
      
      setState(prev => ({
        ...prev,
        alerts: [alert, ...prev.alerts].slice(0, 100), // Keep last 100 alerts
        unreadAlertsCount: prev.unreadAlertsCount + 1
      }))
      
      // Show browser notification for critical alerts
      if (alert.severity === 'CRITICAL' && 'Notification' in window) {
        new Notification(`Crypto Alert: ${alert.symbol}`, {
          body: alert.message,
          icon: '/crypto-icon.png'
        })
      }
    }
  }, [])

  // Subscribe to symbol
  const subscribeToSymbol = useCallback((symbol: string) => {
    if (!webSocket.isConnected) {
      console.warn('Cannot subscribe - WebSocket not connected')
      return
    }

    const destination = `/topic/crypto.${symbol.toLowerCase()}`
    const subscriptionId = webSocket.subscribe({
      destination,
      callback: handlePriceUpdate
    })

    subscriptionIdsRef.current.set(`symbol-${symbol}`, subscriptionId)
    setState(prev => ({
      ...prev,
      subscribedSymbols: new Set([...prev.subscribedSymbols, symbol])
    }))

    console.log(`Subscribed to crypto symbol: ${symbol}`)
  }, [webSocket, handlePriceUpdate])

  // Unsubscribe from symbol
  const unsubscribeFromSymbol = useCallback((symbol: string) => {
    const subscriptionId = subscriptionIdsRef.current.get(`symbol-${symbol}`)
    if (subscriptionId) {
      webSocket.unsubscribe(subscriptionId)
      subscriptionIdsRef.current.delete(`symbol-${symbol}`)
      
      setState(prev => {
        const newSubscribedSymbols = new Set(prev.subscribedSymbols)
        newSubscribedSymbols.delete(symbol)
        return {
          ...prev,
          subscribedSymbols: newSubscribedSymbols
        }
      })
      
      console.log(`Unsubscribed from crypto symbol: ${symbol}`)
    }
  }, [webSocket])

  // Subscribe to market updates
  const subscribeToMarket = useCallback(() => {
    if (!webSocket.isConnected) return

    const subscriptionId = webSocket.subscribe({
      destination: '/topic/crypto.market',
      callback: handleMarketUpdate
    })

    subscriptionIdsRef.current.set('market', subscriptionId)
    console.log('Subscribed to crypto market updates')
  }, [webSocket, handleMarketUpdate])

  // Subscribe to alerts
  const subscribeToAlerts = useCallback(() => {
    if (!webSocket.isConnected) return

    const subscriptionId = webSocket.subscribe({
      destination: '/topic/crypto.alerts',
      callback: handleCryptoAlert
    })

    subscriptionIdsRef.current.set('alerts', subscriptionId)
    console.log('Subscribed to crypto alerts')
  }, [webSocket, handleCryptoAlert])

  // Mark alert as read
  const markAlertAsRead = useCallback((alertId: string) => {
    setState(prev => ({
      ...prev,
      unreadAlertsCount: Math.max(0, prev.unreadAlertsCount - 1)
    }))
  }, [])

  // Clear all alerts
  const clearAllAlerts = useCallback(() => {
    setState(prev => ({
      ...prev,
      alerts: [],
      unreadAlertsCount: 0
    }))
  }, [])

  // Auto-connect and subscribe
  useEffect(() => {
    if (autoConnect) {
      webSocket.connect()
    }

    return () => {
      webSocket.disconnect()
    }
  }, [autoConnect, webSocket])

  // Auto-subscribe to symbols when connected
  useEffect(() => {
    if (webSocket.isConnected) {
      // Subscribe to market updates
      subscribeToMarket()

      // Subscribe to alerts
      subscribeToAlerts()

      // Subscribe to pong responses for ping testing
      const pongSubscriptionId = webSocket.subscribe({
        destination: '/topic/system/pong',
        callback: (message) => {
          console.log('Received pong:', message)
        }
      })

      // Subscribe to individual symbols
      symbols.forEach(symbol => {
        subscribeToSymbol(symbol)
      })

      // Store pong subscription ID for cleanup
      subscriptionIdsRef.current.set('pong', pongSubscriptionId)
    }
  }, [webSocket.isConnected, symbols, subscribeToMarket, subscribeToAlerts, subscribeToSymbol])

  return {
    state,
    actions: {
      subscribeToSymbol,
      unsubscribeFromSymbol,
      subscribeToMarket,
      unsubscribeFromMarket: () => {
        const subscriptionId = subscriptionIdsRef.current.get('market')
        if (subscriptionId) {
          webSocket.unsubscribe(subscriptionId)
          subscriptionIdsRef.current.delete('market')
        }
      },
      subscribeToAlerts,
      markAlertAsRead,
      clearAllAlerts,
      connect: webSocket.connect,
      disconnect: webSocket.disconnect,
      reconnect: webSocket.reconnect,
      refreshData: () => {
        // Trigger manual data refresh
        webSocket.sendMessage('/app/crypto.refresh', { timestamp: new Date().toISOString() })
      },
      clearData: () => {
        setState(prev => ({
          ...prev,
          prices: {},
          marketData: [],
          alerts: [],
          unreadAlertsCount: 0
        }))
      },

      // WebSocket messaging
      sendMessage: webSocket.sendMessage
    }
  }
}
