import { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { Client, IMessage, StompSubscription } from '@stomp/stompjs'
import SockJS from 'sockjs-client'

/**
 * Enterprise WebSocket Hook for React
 * Provides robust WebSocket/STOMP connection management with auto-reconnection,
 * subscription management, and comprehensive error handling
 * 
 * Features:
 * - Auto-reconnection with exponential backoff
 * - Subscription management
 * - Connection state tracking
 * - Message queuing during disconnection
 * - TypeScript support
 * - Performance optimization
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

export interface WebSocketConfig {
  url: string
  debug?: boolean
  reconnectDelay?: number
  maxReconnectAttempts?: number
  heartbeatIncoming?: number
  heartbeatOutgoing?: number
  headers?: Record<string, string>
}

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
  messageId?: string
  priority?: number
}

export interface SubscriptionConfig {
  destination: string
  callback: (message: WebSocketMessage) => void
  headers?: Record<string, string>
}

export enum ConnectionState {
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  RECONNECTING = 'RECONNECTING',
  ERROR = 'ERROR'
}

export interface WebSocketHookReturn {
  // Connection state
  connectionState: ConnectionState
  isConnected: boolean
  error: string | null
  
  // Connection management
  connect: () => void
  disconnect: () => void
  reconnect: () => void
  
  // Subscription management
  subscribe: (config: SubscriptionConfig) => string
  unsubscribe: (subscriptionId: string) => void
  unsubscribeAll: () => void
  
  // Messaging
  sendMessage: (destination: string, message: any, headers?: Record<string, string>) => void
  
  // Statistics
  stats: {
    totalMessages: number
    totalErrors: number
    reconnectAttempts: number
    lastConnected: Date | null
    subscriptionCount: number
  }
}

const DEFAULT_CONFIG: Required<WebSocketConfig> = {
  url: 'http://localhost:8080/ws',
  debug: false,
  reconnectDelay: 10000, // Increased from 5s to 10s to reduce spam
  maxReconnectAttempts: 3, // Reduced from 10 to 3 attempts
  heartbeatIncoming: 10000,
  heartbeatOutgoing: 10000,
  headers: {}
}

export const useWebSocket = (config: WebSocketConfig): WebSocketHookReturn => {
  const fullConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config])
  
  // State management
  const [connectionState, setConnectionState] = useState<ConnectionState>(ConnectionState.DISCONNECTED)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState({
    totalMessages: 0,
    totalErrors: 0,
    reconnectAttempts: 0,
    lastConnected: null as Date | null,
    subscriptionCount: 0
  })

  // Refs for stable references
  const clientRef = useRef<Client | null>(null)
  const subscriptionsRef = useRef<Map<string, StompSubscription>>(new Map())
  const messageQueueRef = useRef<Array<{ destination: string; message: any; headers?: Record<string, string> }>>([])
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttemptsRef = useRef(0)

  // Generate unique subscription ID
  const generateSubscriptionId = useCallback(() => {
    return `sub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }, [])

  // Update statistics
  const updateStats = useCallback((updates: Partial<typeof stats>) => {
    setStats(prev => ({ ...prev, ...updates }))
  }, [])



  // Connect to WebSocket
  const connect = useCallback(() => {
    if (clientRef.current?.connected) {
      console.log('WebSocket already connected')
      return
    }

    setConnectionState(ConnectionState.CONNECTING)
    setError(null)

    try {
      const client = new Client({
        webSocketFactory: () => new SockJS(fullConfig.url),
        connectHeaders: fullConfig.headers,
        debug: fullConfig.debug ? (str) => console.log('STOMP Debug:', str) : undefined,
        reconnectDelay: fullConfig.reconnectDelay,
        heartbeatIncoming: fullConfig.heartbeatIncoming,
        heartbeatOutgoing: fullConfig.heartbeatOutgoing,

        onConnect: () => {
          console.log('WebSocket connected successfully')
          setConnectionState(ConnectionState.CONNECTED)
          setError(null)
          reconnectAttemptsRef.current = 0
          setStats(prev => ({
            ...prev,
            lastConnected: new Date(),
            reconnectAttempts: 0
          }))

          // Process queued messages
          while (messageQueueRef.current.length > 0) {
            const queuedMessage = messageQueueRef.current.shift()
            if (queuedMessage && clientRef.current?.connected) {
              try {
                clientRef.current.publish({
                  destination: queuedMessage.destination,
                  body: JSON.stringify(queuedMessage.message),
                  headers: queuedMessage.headers || {}
                })
              } catch (err) {
                console.error('Failed to send queued message:', err)
              }
            }
          }
        },

        onDisconnect: () => {
          console.log('WebSocket disconnected')
          setConnectionState(ConnectionState.DISCONNECTED)

          // Clear subscriptions
          subscriptionsRef.current.clear()
          setStats(prev => ({ ...prev, subscriptionCount: 0 }))
        },

        onStompError: (frame) => {
          console.error('STOMP Error:', frame)
          setError(`STOMP Error: ${frame.headers.message || 'Unknown error'}`)
          setConnectionState(ConnectionState.ERROR)
          setStats(prev => ({ ...prev, totalErrors: prev.totalErrors + 1 }))
        },

        onWebSocketError: (error) => {
          console.error('WebSocket Error:', error)
          setError(`WebSocket Error: ${error}`)
          setConnectionState(ConnectionState.ERROR)
          setStats(prev => ({ ...prev, totalErrors: prev.totalErrors + 1 }))
        },

        onWebSocketClose: () => {
          console.log('WebSocket connection closed')
          setConnectionState(ConnectionState.DISCONNECTED)
        }
      })

      clientRef.current = client
      client.activate()

    } catch (err) {
      console.error('Failed to create WebSocket connection:', err)
      setError(`Connection failed: ${err}`)
      setConnectionState(ConnectionState.ERROR)
    }
  }, [fullConfig])

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (clientRef.current) {
      console.log('Disconnecting WebSocket...')
      
      // Clear reconnection timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
        reconnectTimeoutRef.current = null
      }
      
      // Deactivate client
      clientRef.current.deactivate()
      clientRef.current = null
      
      // Clear subscriptions
      subscriptionsRef.current.clear()
      
      setConnectionState(ConnectionState.DISCONNECTED)
      updateStats({ subscriptionCount: 0 })
    }
  }, [updateStats])

  // Reconnect to WebSocket
  const reconnect = useCallback(() => {
    disconnect()
    setTimeout(() => connect(), 1000)
  }, [connect, disconnect])



  // Subscribe to destination
  const subscribe = useCallback((config: SubscriptionConfig): string => {
    const subscriptionId = generateSubscriptionId()
    
    if (!clientRef.current?.connected) {
      console.warn('Cannot subscribe - WebSocket not connected')
      return subscriptionId
    }

    try {
      const subscription = clientRef.current.subscribe(
        config.destination,
        (message: IMessage) => {
          try {
            const parsedMessage: WebSocketMessage = JSON.parse(message.body)
            config.callback(parsedMessage)
            setStats(prev => ({ ...prev, totalMessages: prev.totalMessages + 1 }))
          } catch (err) {
            console.error('Error parsing WebSocket message:', err)
            setStats(prev => ({ ...prev, totalErrors: prev.totalErrors + 1 }))
          }
        },
        config.headers || {}
      )

      subscriptionsRef.current.set(subscriptionId, subscription)
      setStats(prev => ({ ...prev, subscriptionCount: prev.subscriptionCount + 1 }))

      console.log(`Subscribed to ${config.destination} with ID: ${subscriptionId}`)

    } catch (err) {
      console.error('Subscription failed:', err)
      setStats(prev => ({ ...prev, totalErrors: prev.totalErrors + 1 }))
    }

    return subscriptionId
  }, [generateSubscriptionId])

  // Unsubscribe from destination
  const unsubscribe = useCallback((subscriptionId: string) => {
    const subscription = subscriptionsRef.current.get(subscriptionId)
    if (subscription) {
      subscription.unsubscribe()
      subscriptionsRef.current.delete(subscriptionId)
      setStats(prev => ({ ...prev, subscriptionCount: prev.subscriptionCount - 1 }))
      console.log(`Unsubscribed from subscription ID: ${subscriptionId}`)
    }
  }, [])

  // Unsubscribe from all destinations
  const unsubscribeAll = useCallback(() => {
    subscriptionsRef.current.forEach((subscription) => {
      subscription.unsubscribe()
    })
    subscriptionsRef.current.clear()
    setStats(prev => ({ ...prev, subscriptionCount: 0 }))
    console.log('Unsubscribed from all destinations')
  }, [])

  // Send message
  const sendMessage = useCallback((destination: string, message: unknown, headers?: Record<string, string>) => {
    if (!clientRef.current?.connected) {
      console.warn('Cannot send message - WebSocket not connected. Queuing message.')
      messageQueueRef.current.push({ destination, message, headers })
      return
    }

    try {
      clientRef.current.publish({
        destination,
        body: JSON.stringify(message),
        headers: headers || {}
      })
      console.log(`Message sent to ${destination}`)
    } catch (err) {
      console.error('Failed to send message:', err)
      setStats(prev => ({ ...prev, totalErrors: prev.totalErrors + 1 }))
    }
  }, [])



  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    connectionState,
    isConnected: connectionState === ConnectionState.CONNECTED,
    error,
    connect,
    disconnect,
    reconnect,
    subscribe,
    unsubscribe,
    unsubscribeAll,
    sendMessage,
    stats
  }
}
