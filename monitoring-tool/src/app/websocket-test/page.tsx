'use client'

import React, { useState } from 'react'
import { useWebSocket, ConnectionState } from '@/hooks/useWebSocket'

/**
 * WebSocket Test Page
 * Simple page to test WebSocket connection behavior
 */
export default function WebSocketTestPage() {
  const [autoConnect, setAutoConnect] = useState(false)
  const [backendHealth, setBackendHealth] = useState<'checking' | 'healthy' | 'unhealthy'>('checking')
  const [healthCheckError, setHealthCheckError] = useState<string | null>(null)

  const webSocket = useWebSocket({
    url: process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:8080/ws',
    debug: true,
    reconnectDelay: 5000,
    maxReconnectAttempts: 5
  })

  // Check backend health
  const checkBackendHealth = async () => {
    setBackendHealth('checking')
    setHealthCheckError(null)

    try {
      const response = await fetch('http://localhost:8080/api/v1/test/health', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        setBackendHealth('healthy')
        const data = await response.json()
        console.log('Backend health response:', data)
      } else {
        setBackendHealth('unhealthy')
        setHealthCheckError(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      setBackendHealth('unhealthy')
      setHealthCheckError(error instanceof Error ? error.message : 'Unknown error')
    }
  }

  // Check backend health on component mount
  React.useEffect(() => {
    checkBackendHealth()
  }, [])

  const getConnectionStatusColor = () => {
    switch (webSocket.connectionState) {
      case ConnectionState.CONNECTED:
        return 'text-green-600'
      case ConnectionState.CONNECTING:
      case ConnectionState.RECONNECTING:
        return 'text-yellow-600'
      case ConnectionState.ERROR:
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getConnectionStatusText = () => {
    switch (webSocket.connectionState) {
      case ConnectionState.CONNECTED:
        return 'Connected'
      case ConnectionState.CONNECTING:
        return 'Connecting...'
      case ConnectionState.RECONNECTING:
        return 'Reconnecting...'
      case ConnectionState.ERROR:
        return 'Error'
      case ConnectionState.DISCONNECTED:
        return 'Disconnected'
      default:
        return 'Unknown'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">WebSocket Connection Test</h1>
          
          {/* Backend Health Status */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">Backend Health</h2>
            <div className="flex items-center gap-2">
              <div className={`text-lg font-medium ${
                backendHealth === 'healthy' ? 'text-green-600' :
                backendHealth === 'unhealthy' ? 'text-red-600' : 'text-yellow-600'
              }`}>
                {backendHealth === 'healthy' ? '✅ Healthy' :
                 backendHealth === 'unhealthy' ? '❌ Unhealthy' : '⏳ Checking...'}
              </div>
              <button
                onClick={checkBackendHealth}
                className="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
              >
                Refresh
              </button>
            </div>
            {healthCheckError && (
              <div className="text-red-600 text-sm mt-1">
                Error: {healthCheckError}
              </div>
            )}
          </div>

          {/* Connection Status */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">WebSocket Connection Status</h2>
            <div className={`text-lg font-medium ${getConnectionStatusColor()}`}>
              {getConnectionStatusText()}
            </div>
            {webSocket.error && (
              <div className="text-red-600 text-sm mt-1">
                Error: {webSocket.error}
              </div>
            )}
          </div>

          {/* Connection Controls */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">Controls</h2>
            <div className="flex gap-2">
              <button
                onClick={webSocket.connect}
                disabled={webSocket.isConnected}
                className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Connect
              </button>
              <button
                onClick={webSocket.disconnect}
                disabled={!webSocket.isConnected}
                className="px-4 py-2 bg-red-500 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Disconnect
              </button>
              <button
                onClick={webSocket.reconnect}
                className="px-4 py-2 bg-yellow-500 text-white rounded"
              >
                Reconnect
              </button>
            </div>
          </div>

          {/* Statistics */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">Statistics</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">Total Messages</div>
                <div className="text-lg font-semibold">{webSocket.stats.totalMessages}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">Total Errors</div>
                <div className="text-lg font-semibold">{webSocket.stats.totalErrors}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">Reconnect Attempts</div>
                <div className="text-lg font-semibold">{webSocket.stats.reconnectAttempts}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">Subscriptions</div>
                <div className="text-lg font-semibold">{webSocket.stats.subscriptionCount}</div>
              </div>
            </div>
          </div>

          {/* Last Connected */}
          {webSocket.stats.lastConnected && (
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2">Last Connected</h2>
              <div className="text-gray-600">
                {webSocket.stats.lastConnected.toLocaleString()}
              </div>
            </div>
          )}

          {/* Test Message */}
          {webSocket.isConnected && (
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2">Send Test Message</h2>
              <button
                onClick={() => {
                  webSocket.sendMessage('/app/test', { 
                    message: 'Hello from WebSocket test page',
                    timestamp: new Date().toISOString()
                  })
                }}
                className="px-4 py-2 bg-green-500 text-white rounded"
              >
                Send Test Message
              </button>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded">
            <h3 className="font-semibold text-blue-900 mb-2">Instructions</h3>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• <strong>Step 1:</strong> Ensure backend health shows "✅ Healthy"</li>
              <li>• <strong>Step 2:</strong> Click "Connect" to establish WebSocket connection</li>
              <li>• <strong>Step 3:</strong> Monitor connection status and statistics</li>
              <li>• <strong>Debug:</strong> Check browser console for detailed logs</li>
              <li>• <strong>Backend:</strong> Server should be running on localhost:8080</li>
              <li>• <strong>Reconnection:</strong> Maximum 5 attempts with 5-second delays</li>
            </ul>
          </div>

          {/* Troubleshooting */}
          {backendHealth === 'unhealthy' && (
            <div className="bg-red-50 p-4 rounded mt-4">
              <h3 className="font-semibold text-red-900 mb-2">Troubleshooting</h3>
              <ul className="text-red-800 text-sm space-y-1">
                <li>• Start the backend server: <code className="bg-red-100 px-1 rounded">java -jar banking-backend.jar</code></li>
                <li>• Check if port 8080 is available</li>
                <li>• Verify the health endpoint: <a href="http://localhost:8080/api/v1/test/health" target="_blank" className="underline">http://localhost:8080/api/v1/test/health</a></li>
                <li>• Check firewall and network settings</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
