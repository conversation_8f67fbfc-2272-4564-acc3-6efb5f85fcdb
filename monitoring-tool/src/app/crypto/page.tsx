'use client'

import React from 'react'
import CryptoMonitoringDashboard from '@/components/crypto/CryptoMonitoringDashboard'

/**
 * Crypto Monitoring Page
 * Main page for cryptocurrency monitoring with real-time WebSocket updates
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
export default function CryptoPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <CryptoMonitoringDashboard
        initialSymbols={['BTC', 'ETH', 'ADA', 'DOT', 'LINK', 'MATIC', 'SOL', 'AVAX']}
        autoConnect={true}
        showAlerts={true}
        showConnectionStatus={true}
      />
    </div>
  )
}
