'use client'

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert } from '@/types'
import { AlertTriangle, CheckCircle, Clock, X } from 'lucide-react'
// import { formatDistanceToNow } from 'date-fns'

// Temporary replacement for date-fns
const formatDistanceToNow = (date: Date, options?: { addSuffix?: boolean }) => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))

  if (diffMinutes < 1) return options?.addSuffix ? 'just now' : '0 minutes'
  if (diffMinutes < 60) return options?.addSuffix ? `${diffMinutes} minutes ago` : `${diffMinutes} minutes`

  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours < 24) return options?.addSuffix ? `${diffHours} hours ago` : `${diffHours} hours`

  const diffDays = Math.floor(diffHours / 24)
  return options?.addSuffix ? `${diffDays} days ago` : `${diffDays} days`
}

interface AlertsListProps {
  alerts: Alert[]
}

const getSeverityColor = (severity: Alert['severity']) => {
  switch (severity) {
    case 'critical':
      return 'destructive'
    case 'high':
      return 'destructive'
    case 'medium':
      return 'secondary'
    case 'low':
      return 'outline'
    default:
      return 'outline'
  }
}

const getSeverityIcon = (severity: Alert['severity']) => {
  const iconProps = { className: 'h-4 w-4' }
  
  switch (severity) {
    case 'critical':
    case 'high':
      return <AlertTriangle {...iconProps} className="h-4 w-4 text-red-500" />
    case 'medium':
      return <AlertTriangle {...iconProps} className="h-4 w-4 text-yellow-500" />
    case 'low':
      return <AlertTriangle {...iconProps} className="h-4 w-4 text-blue-500" />
    default:
      return <AlertTriangle {...iconProps} className="h-4 w-4 text-gray-500" />
  }
}

const AlertItem = ({ alert }: { alert: Alert }) => {
  const severityColor = getSeverityColor(alert.severity)
  const severityIcon = getSeverityIcon(alert.severity)
  const timeAgo = formatDistanceToNow(alert.timestamp, { addSuffix: true })
  const resolvedTimeAgo = alert.resolvedAt 
    ? formatDistanceToNow(alert.resolvedAt, { addSuffix: true })
    : null

  return (
    <div className={`p-4 border rounded-lg transition-all duration-200 ${
      alert.resolved 
        ? 'bg-gray-50 border-gray-200' 
        : 'bg-white border-gray-300 hover:shadow-sm'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3 flex-1">
          <div className="mt-0.5">
            {alert.resolved ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              severityIcon
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <Badge variant={severityColor} className="text-xs">
                {alert.severity}
              </Badge>
              {alert.resolved && (
                <Badge variant="outline" className="text-xs text-green-600">
                  Resolved
                </Badge>
              )}
            </div>
            
            <p className={`text-sm font-medium ${
              alert.resolved ? 'text-gray-600' : 'text-gray-900'
            }`}>
              {alert.message}
            </p>
            
            <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>Triggered {timeAgo}</span>
              </div>
              
              {alert.resolved && resolvedTimeAgo && (
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  <span>Resolved {resolvedTimeAgo}</span>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {!alert.resolved && (
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}

export function AlertsList({ alerts }: AlertsListProps) {
  const activeAlerts = alerts.filter(alert => !alert.resolved)
  const resolvedAlerts = alerts.filter(alert => alert.resolved)

  if (alerts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Alerts</CardTitle>
          <CardDescription>No alerts to display</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
            <p>All systems are running smoothly!</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Alerts</CardTitle>
            <CardDescription>
              {activeAlerts.length} active, {resolvedAlerts.length} resolved
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            {activeAlerts.length > 0 && (
              <Badge variant="destructive">
                {activeAlerts.length} Active
              </Badge>
            )}
            {resolvedAlerts.length > 0 && (
              <Badge variant="outline">
                {resolvedAlerts.length} Resolved
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Active Alerts */}
          {activeAlerts.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                Active Alerts
              </h4>
              <div className="space-y-3">
                {activeAlerts.map((alert) => (
                  <AlertItem key={alert.id} alert={alert} />
                ))}
              </div>
            </div>
          )}
          
          {/* Resolved Alerts */}
          {resolvedAlerts.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                Recently Resolved
              </h4>
              <div className="space-y-3">
                {resolvedAlerts.slice(0, 5).map((alert) => (
                  <AlertItem key={alert.id} alert={alert} />
                ))}
              </div>
              
              {resolvedAlerts.length > 5 && (
                <div className="text-center mt-4">
                  <Button variant="ghost" size="sm">
                    View All Resolved Alerts
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
