'use client'

import {
  <PERSON><PERSON>,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar'
import {
  Home,
  Monitor,
  User,
  BookOpen,
  TrendingUp,
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

// Helper function to get the correct URL with basePath
const getUrl = (path: string) => {
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH || (process.env.NODE_ENV === 'production' ? '/monitoring-tool' : '')
  return `${basePath}${path}`
}

const data = {
  navMain: [
    {
      title: 'Dashboard',
      url: getUrl('/'),
      icon: Home,
      isActive: true,
    },
    {
      title: 'Crypto Monitor',
      url: getUrl('/crypto'),
      icon: TrendingUp,
    },
    {
      title: 'Profile',
      url: getUrl('/profile'),
      icon: User,
    },
    {
      title: 'Articles',
      url: getUrl('/profile/articles'),
      icon: BookOpen,
    },
  ],
}

export function AppSidebar() {
  const pathname = usePathname()

  // Helper function to check if current path matches the nav item
  const isActiveRoute = (itemUrl: string) => {
    const basePath = process.env.NEXT_PUBLIC_BASE_PATH || (process.env.NODE_ENV === 'production' ? '/monitoring-tool' : '')
    const cleanItemUrl = itemUrl.replace(basePath, '') || '/'
    return pathname === cleanItemUrl
  }

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Monitor className="h-4 w-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">MonitorPro</span>
            <span className="truncate text-xs text-muted-foreground">
              System Monitoring
            </span>
          </div>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {data.navMain.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActiveRoute(item.url)}
                    tooltip={item.title}
                  >
                    <Link href={item.url}>
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>

                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      
      <SidebarFooter>
        <div className="p-4 text-xs text-muted-foreground">
          <div className="flex items-center justify-between">
            <span>System Status</span>
            <div className="flex items-center gap-1">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Online</span>
            </div>
          </div>
          <div className="mt-2 text-xs">
            Last updated: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </SidebarFooter>
      
      <SidebarRail />
    </Sidebar>
  )
}
