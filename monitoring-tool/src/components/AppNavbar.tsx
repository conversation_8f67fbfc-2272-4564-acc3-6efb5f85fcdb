'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { 
  Menu, 
  X, 
  Home, 
  User, 
  TrendingUp, 
  CreditCard, 
  MessageSquare, 
  Calendar, 
  CheckSquare, 
  Bell, 
  Brain,
  BarChart3
} from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/', icon: <BarChart3 className="h-4 w-4" /> },
  { name: '<PERSON>h<PERSON>ch Hàng', href: '/customer-dashboard', icon: <User className="h-4 w-4" /> },
  { name: '<PERSON><PERSON><PERSON>', href: '/loan-application', icon: <TrendingUp className="h-4 w-4" /> },
  { name: 'Thẻ Tín Dụng', href: '/credit-cards', icon: <CreditCard className="h-4 w-4" /> },
  { name: 'Chatbot AI', href: '/internal-chatbot', icon: <MessageSquare className="h-4 w-4" /> },
  { name: '<PERSON><PERSON>ch Hẹn', href: '/appointments', icon: <Calendar className="h-4 w-4" /> },
  { name: '<PERSON><PERSON><PERSON>n Lý Task', href: '/tasks', icon: <CheckSquare className="h-4 w-4" /> },
  { name: 'Thông Báo', href: '/notifications', icon: <Bell className="h-4 w-4" /> },
  { name: 'Tư Vấn AI', href: '/product-advisory', icon: <Brain className="h-4 w-4" /> },
  { name: 'Crypto Monitor', href: '/crypto', icon: <TrendingUp className="h-4 w-4" /> },
  { name: 'WebSocket Test', href: '/websocket-test', icon: <MessageSquare className="h-4 w-4" /> },
]

export function AppNavbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const pathname = usePathname()

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <h1 className="text-xl font-bold text-gray-900">Banking System</h1>
            </div>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link key={item.name} href={item.href}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    {item.icon}
                    {item.name}
                  </Button>
                </Link>
              )
            })}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {mobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link key={item.name} href={item.href}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    className="w-full justify-start flex items-center gap-2"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item.icon}
                    {item.name}
                  </Button>
                </Link>
              )
            })}
          </div>
        </div>
      )}
    </nav>
  )
}
