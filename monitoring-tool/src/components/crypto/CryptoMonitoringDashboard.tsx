'use client'

import React, { useState, useEffect } from 'react'
import { useCryptoMonitoring, CryptoPriceData, CryptoAlert } from '@/hooks/useCryptoMonitoring'

/**
 * Crypto Monitoring Dashboard Component
 * Real-time cryptocurrency monitoring with WebSocket integration
 * 
 * Features:
 * - Real-time price updates
 * - Interactive price charts
 * - Alert notifications
 * - Symbol management
 * - Connection status monitoring
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

interface CryptoMonitoringDashboardProps {
  initialSymbols?: string[]
  autoConnect?: boolean
  showAlerts?: boolean
  showConnectionStatus?: boolean
}

export const CryptoMonitoringDashboard: React.FC<CryptoMonitoringDashboardProps> = ({
  initialSymbols = ['BTC', 'ETH', 'ADA', 'DOT', 'LINK'],
  autoConnect = true,
  showAlerts = true,
  showConnectionStatus = true
}) => {
  const { state, actions } = useCryptoMonitoring(initialSymbols, autoConnect)
  const [selectedSymbol, setSelectedSymbol] = useState<string>('BTC')
  const [newSymbol, setNewSymbol] = useState<string>('')

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [])

  const handleAddSymbol = () => {
    if (newSymbol.trim() && !state.subscribedSymbols.has(newSymbol.toUpperCase())) {
      actions.subscribeToSymbol(newSymbol.toUpperCase())
      setNewSymbol('')
    }
  }

  const handleRemoveSymbol = (symbol: string) => {
    actions.unsubscribeFromSymbol(symbol)
  }

  const formatPrice = (price: number): string => {
    if (price < 1) {
      return `$${price.toFixed(6)}`
    } else if (price < 1000) {
      return `$${price.toFixed(2)}`
    } else {
      return `$${price.toLocaleString()}`
    }
  }

  const formatPercentage = (percentage: number): string => {
    const sign = percentage >= 0 ? '+' : ''
    return `${sign}${percentage.toFixed(2)}%`
  }

  const getPercentageColor = (percentage: number): string => {
    if (percentage > 0) return 'text-green-600'
    if (percentage < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200'
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'LOW': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Crypto Monitoring Dashboard
        </h1>
        <p className="text-gray-600">
          Real-time cryptocurrency price monitoring and alerts
        </p>
      </div>

      {/* Connection Status */}
      {showConnectionStatus && (
        <div className="mb-6 p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${
                state.isConnected ? 'bg-green-500' : 'bg-red-500'
              }`} />
              <span className="font-medium">
                {state.isConnected ? 'Connected' : 'Disconnected'}
              </span>
              {state.lastUpdate && (
                <span className="text-sm text-gray-500">
                  Last update: {state.lastUpdate.toLocaleTimeString()}
                </span>
              )}
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={actions.connect}
                disabled={state.isConnected}
                className="px-3 py-1 text-sm bg-blue-500 text-white rounded disabled:opacity-50"
              >
                Connect
              </button>
              <button
                onClick={actions.disconnect}
                disabled={!state.isConnected}
                className="px-3 py-1 text-sm bg-red-500 text-white rounded disabled:opacity-50"
              >
                Disconnect
              </button>
              <button
                onClick={actions.refreshData}
                className="px-3 py-1 text-sm bg-green-500 text-white rounded"
              >
                Refresh
              </button>
              <button
                onClick={() => {
                  // Test WebSocket ping
                  if (state.isConnected) {
                    const testMessage = { timestamp: new Date().toISOString(), test: true }
                    console.log('Sending ping message:', testMessage)
                    actions.sendMessage('/app/ping', testMessage)
                  }
                }}
                disabled={!state.isConnected}
                className="px-3 py-1 text-sm bg-purple-500 text-white rounded disabled:opacity-50"
              >
                Test Ping
              </button>
            </div>
          </div>
          
          {state.error && (
            <div className="mt-2 p-2 bg-red-100 text-red-700 rounded text-sm">
              Error: {state.error}
            </div>
          )}
        </div>
      )}

      {/* Symbol Management */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Tracked Symbols</h3>
        
        <div className="flex flex-wrap gap-2 mb-3">
          {Array.from(state.subscribedSymbols).map(symbol => (
            <div key={symbol} className="flex items-center bg-white px-3 py-1 rounded-full border">
              <span className="font-medium">{symbol}</span>
              <button
                onClick={() => handleRemoveSymbol(symbol)}
                className="ml-2 text-red-500 hover:text-red-700"
              >
                ×
              </button>
            </div>
          ))}
        </div>
        
        <div className="flex space-x-2">
          <input
            type="text"
            value={newSymbol}
            onChange={(e) => setNewSymbol(e.target.value.toUpperCase())}
            placeholder="Enter symbol (e.g., BTC)"
            className="px-3 py-2 border rounded-md"
            onKeyPress={(e) => e.key === 'Enter' && handleAddSymbol()}
          />
          <button
            onClick={handleAddSymbol}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Add Symbol
          </button>
        </div>
      </div>

      {/* Price Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
        {Object.values(state.prices).map((crypto: CryptoPriceData) => (
          <div
            key={crypto.symbol}
            className={`p-4 bg-white rounded-lg border-2 cursor-pointer transition-all ${
              selectedSymbol === crypto.symbol ? 'border-blue-500 shadow-lg' : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedSymbol(crypto.symbol)}
          >
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="text-lg font-bold">{crypto.symbol}</h3>
                <p className="text-sm text-gray-600">{crypto.name}</p>
              </div>
              <div className="text-xs text-gray-500">
                #{crypto.rank || 'N/A'}
              </div>
            </div>
            
            <div className="mb-2">
              <div className="text-2xl font-bold">
                {formatPrice(crypto.price_usd)}
              </div>
              {crypto.price_btc && (
                <div className="text-sm text-gray-600">
                  ₿{crypto.price_btc.toFixed(8)}
                </div>
              )}
            </div>
            
            {crypto.percent_change_24h !== undefined && (
              <div className={`text-sm font-medium ${getPercentageColor(crypto.percent_change_24h)}`}>
                24h: {formatPercentage(crypto.percent_change_24h)}
              </div>
            )}
            
            <div className="mt-2 text-xs text-gray-500">
              Updated: {new Date(crypto.timestamp).toLocaleTimeString()}
            </div>
          </div>
        ))}
      </div>

      {/* Alerts Panel */}
      {showAlerts && state.alerts.length > 0 && (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-semibold">
              Recent Alerts ({state.unreadAlertsCount} unread)
            </h3>
            <button
              onClick={actions.clearAllAlerts}
              className="text-sm text-red-600 hover:text-red-800"
            >
              Clear All
            </button>
          </div>
          
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {state.alerts.slice(0, 10).map((alert: CryptoAlert) => (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border ${getSeverityColor(alert.severity)}`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-medium">{alert.symbol}</div>
                    <div className="text-sm">{alert.message}</div>
                  </div>
                  <div className="text-xs">
                    {new Date(alert.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="p-4 bg-white rounded-lg border">
          <div className="text-2xl font-bold text-blue-600">{Object.keys(state.prices).length}</div>
          <div className="text-sm text-gray-600">Tracked Symbols</div>
        </div>
        
        <div className="p-4 bg-white rounded-lg border">
          <div className="text-2xl font-bold text-green-600">{state.updateCount}</div>
          <div className="text-sm text-gray-600">Price Updates</div>
        </div>
        
        <div className="p-4 bg-white rounded-lg border">
          <div className="text-2xl font-bold text-orange-600">{state.alerts.length}</div>
          <div className="text-sm text-gray-600">Total Alerts</div>
        </div>
        
        <div className="p-4 bg-white rounded-lg border">
          <div className="text-2xl font-bold text-purple-600">
            {state.isConnected ? 'Online' : 'Offline'}
          </div>
          <div className="text-sm text-gray-600">Connection Status</div>
        </div>
      </div>
    </div>
  )
}

export default CryptoMonitoringDashboard
