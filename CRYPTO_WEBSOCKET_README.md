# Real-time Crypto Monitoring System

Enterprise-grade real-time cryptocurrency monitoring system with WebSocket integration, built with Spring Boot backend and Next.js frontend.

## 🚀 Features

### Backend (Spring Boot)
- **Real-time WebSocket Communication**: STOMP protocol with SockJS fallback
- **Crypto Data Collection**: Automated price fetching from multiple APIs (CoinGecko, CoinMarketCap)
- **Scheduled Jobs**: Quartz-based job scheduling for data collection
- **Enterprise Security**: JWT authentication, rate limiting, CORS configuration
- **Scalable Architecture**: Microservices-ready with Redis caching support
- **Comprehensive Monitoring**: Connection tracking, metrics, error handling

### Frontend (Next.js + React)
- **Real-time Dashboard**: Live crypto price updates via WebSocket
- **Interactive UI**: Modern React components with TailwindCSS
- **Connection Management**: Auto-reconnection, connection status monitoring
- **Alert System**: Real-time notifications for price changes
- **Responsive Design**: Mobile-friendly interface
- **TypeScript Support**: Full type safety and IntelliSense

## 🏗️ Architecture

```
┌─────────────────┐    WebSocket/STOMP    ┌─────────────────┐
│   Next.js App   │◄─────────────────────►│  Spring Boot    │
│                 │                       │     Backend     │
│ - React Hooks   │                       │                 │
│ - WebSocket     │                       │ - STOMP Config  │
│ - Dashboard     │                       │ - Job Scheduler │
└─────────────────┘                       │ - Crypto APIs   │
                                          └─────────────────┘
                                                    │
                                                    ▼
                                          ┌─────────────────┐
                                          │  External APIs  │
                                          │                 │
                                          │ - CoinGecko     │
                                          │ - CoinMarketCap │
                                          └─────────────────┘
```

## 🛠️ Setup Instructions

### Prerequisites
- Java 17+
- Node.js 18+
- Maven 3.6+
- npm/yarn

### Backend Setup

1. **Navigate to backend directory**:
   ```bash
   cd banking-base-api
   ```

2. **Configure application properties**:
   ```yaml
   # src/main/resources/application-dev.yml
   banking:
     crypto:
       collection:
         enabled: true
         cron: "0 */1 * * * ?"  # Every minute
         symbols: "BTC,ETH,ADA,DOT,LINK,MATIC,SOL,AVAX"
       api:
         coingecko:
           url: "https://api.coingecko.com/api/v3"
         coinmarketcap:
           url: "https://pro-api.coinmarketcap.com/v1"
           key: "YOUR_API_KEY_HERE"  # Optional
   ```

3. **Run the application**:
   ```bash
   mvn spring-boot:run -Dspring-boot.run.profiles=dev
   ```

4. **Verify WebSocket endpoint**:
   - WebSocket URL: `http://localhost:8080/ws`
   - REST API: `http://localhost:8080/api/v1/websocket/info`

### Frontend Setup

1. **Navigate to frontend directory**:
   ```bash
   cd monitoring-tool
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Configure environment variables**:
   ```bash
   # .env.local
   NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1
   NEXT_PUBLIC_WS_URL=http://localhost:8080/ws
   NODE_ENV=development
   ```

4. **Run the development server**:
   ```bash
   npm run dev
   ```

5. **Access the application**:
   - Main Dashboard: `http://localhost:3000`
   - Crypto Monitor: `http://localhost:3000/crypto`

## 📡 WebSocket API

### Connection
```javascript
// Connect to WebSocket
const client = new Client({
  webSocketFactory: () => new SockJS('http://localhost:8080/ws'),
  connectHeaders: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'X-User-Id': 'user123'
  }
});
```

### Subscriptions

#### Market Data
```javascript
// Subscribe to general market updates
client.subscribe('/topic/crypto.market', (message) => {
  const data = JSON.parse(message.body);
  console.log('Market update:', data);
});

// Subscribe to specific cryptocurrency
client.subscribe('/topic/crypto.btc', (message) => {
  const data = JSON.parse(message.body);
  console.log('BTC update:', data);
});
```

#### Alerts
```javascript
// Subscribe to price alerts
client.subscribe('/topic/crypto.alerts', (message) => {
  const alert = JSON.parse(message.body);
  console.log('Price alert:', alert);
});
```

#### User-specific Notifications
```javascript
// Subscribe to personal notifications
client.subscribe('/user/queue/notifications', (message) => {
  const notification = JSON.parse(message.body);
  console.log('Personal notification:', notification);
});
```

### Message Formats

#### Price Update
```json
{
  "type": "PRICE_UPDATE",
  "symbol": "BTC",
  "data": {
    "symbol": "BTC",
    "name": "Bitcoin",
    "price_usd": 45000.50,
    "percent_change_24h": 2.5,
    "market_cap_usd": ************,
    "volume_24h_usd": ***********,
    "timestamp": "2024-01-15T10:30:00.000Z"
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

#### Price Alert
```json
{
  "type": "PRICE_ALERT",
  "symbol": "ETH",
  "message": "ETH price changed by 5.2% to $3,200.00",
  "severity": "HIGH",
  "data": { /* price data */ },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 🔧 Configuration

### Job Scheduling
```yaml
banking:
  crypto:
    collection:
      cron: "0 */1 * * * ?"     # Every minute
      timeout: 30000            # 30 seconds
      max-retries: 3
      batch-size: 10
  scheduler:
    enabled: true
    thread-pool-size: 5
```

### WebSocket Settings
```yaml
# WebSocket configuration in WebSocketConfig.java
- Heartbeat: 10 seconds
- Connection timeout: 30 seconds
- Max connections: 1000
- Rate limiting: 5 connections per IP
```

## 🚀 Usage Examples

### React Hook Usage
```typescript
import { useCryptoMonitoring } from '@/hooks/useCryptoMonitoring';

function CryptoComponent() {
  const { state, actions } = useCryptoMonitoring(['BTC', 'ETH'], true);
  
  useEffect(() => {
    if (state.isConnected) {
      console.log('Connected to WebSocket');
      console.log('Current prices:', state.prices);
    }
  }, [state.isConnected, state.prices]);

  return (
    <div>
      <h2>Connection: {state.isConnected ? 'Connected' : 'Disconnected'}</h2>
      {Object.values(state.prices).map(crypto => (
        <div key={crypto.symbol}>
          {crypto.symbol}: ${crypto.price_usd}
        </div>
      ))}
    </div>
  );
}
```

### Manual API Trigger
```bash
# Trigger manual crypto data collection
curl -X POST http://localhost:8080/api/v1/websocket/crypto/collect \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 📊 Monitoring & Metrics

### Connection Statistics
```bash
# Get WebSocket statistics
curl http://localhost:8080/api/v1/websocket/stats \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Job Status
```bash
# Get crypto job status
curl http://localhost:8080/api/v1/websocket/crypto/job-status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🔒 Security Features

- **JWT Authentication**: Secure WebSocket connections
- **Rate Limiting**: Prevent connection abuse
- **CORS Configuration**: Secure cross-origin requests
- **Input Validation**: Sanitize all user inputs
- **Connection Monitoring**: Track and limit connections

## 🐛 Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check if backend is running on port 8080
   - Verify CORS configuration
   - Check authentication token

2. **No Price Updates**
   - Verify crypto job is enabled
   - Check API rate limits
   - Review application logs

3. **Frontend Build Issues**
   - Clear node_modules and reinstall
   - Check TypeScript errors
   - Verify environment variables

### Debug Mode
```bash
# Enable debug logging
export LOGGING_LEVEL_COM_BANKING=DEBUG
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## 📈 Performance Optimization

- **Connection Pooling**: Efficient WebSocket management
- **Message Batching**: Reduce network overhead
- **Caching**: Redis integration for price data
- **Rate Limiting**: API call optimization
- **Lazy Loading**: Frontend component optimization

## 🔄 Future Enhancements

- [ ] Database persistence for historical data
- [ ] Advanced charting with technical indicators
- [ ] User-specific watchlists and alerts
- [ ] Mobile app with push notifications
- [ ] Advanced analytics and reporting
- [ ] Multi-exchange data aggregation

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation wiki
