#!/bin/bash

# Real-time Crypto Monitoring System Startup Script
# This script starts both the Spring Boot backend and Next.js frontend

set -e

echo "🚀 Starting Real-time Crypto Monitoring System..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed. Please install Java 17 or higher."
        exit 1
    fi
    
    if ! command -v mvn &> /dev/null; then
        print_error "Maven is not installed. Please install Maven 3.6 or higher."
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    print_success "All prerequisites are installed."
}

# Function to start backend
start_backend() {
    print_status "Starting Spring Boot backend..."
    
    cd banking-base-api
    
    # Check if application.yml exists
    if [ ! -f "src/main/resources/application-dev.yml" ]; then
        print_error "application-dev.yml not found. Please ensure the configuration file exists."
        exit 1
    fi
    
    # Start Spring Boot application in background
    print_status "Building and starting Spring Boot application..."
    mvn spring-boot:run -Dspring-boot.run.profiles=dev > ../backend.log 2>&1 &
    BACKEND_PID=$!
    
    # Wait for backend to start
    print_status "Waiting for backend to start..."
    sleep 10
    
    # Check if backend is running
    if ps -p $BACKEND_PID > /dev/null; then
        print_success "Backend started successfully (PID: $BACKEND_PID)"
        echo $BACKEND_PID > ../backend.pid
    else
        print_error "Failed to start backend. Check backend.log for details."
        exit 1
    fi
    
    cd ..
}

# Function to start frontend
start_frontend() {
    print_status "Starting Next.js frontend..."
    
    cd monitoring-tool
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Please ensure you're in the correct directory."
        exit 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing npm dependencies..."
        npm install
    fi
    
    # Check if .env.local exists
    if [ ! -f ".env.local" ]; then
        print_warning ".env.local not found. Creating default configuration..."
        cat > .env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1
NEXT_PUBLIC_WS_URL=http://localhost:8080/ws
NODE_ENV=development
EOF
    fi
    
    # Start Next.js application in background
    print_status "Starting Next.js development server..."
    npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    # Wait for frontend to start
    print_status "Waiting for frontend to start..."
    sleep 5
    
    # Check if frontend is running
    if ps -p $FRONTEND_PID > /dev/null; then
        print_success "Frontend started successfully (PID: $FRONTEND_PID)"
        echo $FRONTEND_PID > ../frontend.pid
    else
        print_error "Failed to start frontend. Check frontend.log for details."
        exit 1
    fi
    
    cd ..
}

# Function to check service health
check_health() {
    print_status "Checking service health..."
    
    # Check backend health
    sleep 5
    if curl -s http://localhost:8080/api/v1/websocket/info > /dev/null; then
        print_success "Backend is healthy and responding"
    else
        print_warning "Backend health check failed. It may still be starting up."
    fi
    
    # Check frontend health
    if curl -s http://localhost:3000 > /dev/null; then
        print_success "Frontend is healthy and responding"
    else
        print_warning "Frontend health check failed. It may still be starting up."
    fi
}

# Function to display running services
show_services() {
    echo ""
    echo "🎉 Crypto Monitoring System is now running!"
    echo "=========================================="
    echo ""
    echo "📊 Services:"
    echo "  • Backend (Spring Boot):  http://localhost:8080"
    echo "  • Frontend (Next.js):     http://localhost:3000"
    echo "  • WebSocket Endpoint:     ws://localhost:8080/ws"
    echo ""
    echo "🔗 Quick Links:"
    echo "  • Main Dashboard:         http://localhost:3000"
    echo "  • Crypto Monitor:         http://localhost:3000/crypto"
    echo "  • WebSocket Info API:     http://localhost:8080/api/v1/websocket/info"
    echo "  • Job Status API:         http://localhost:8080/api/v1/websocket/crypto/job-status"
    echo ""
    echo "📝 Logs:"
    echo "  • Backend logs:           tail -f backend.log"
    echo "  • Frontend logs:          tail -f frontend.log"
    echo ""
    echo "🛑 To stop services:"
    echo "  • Run: ./stop-crypto-monitoring.sh"
    echo "  • Or press Ctrl+C to stop this script"
    echo ""
}

# Function to cleanup on exit
cleanup() {
    print_status "Shutting down services..."
    
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if ps -p $BACKEND_PID > /dev/null; then
            print_status "Stopping backend (PID: $BACKEND_PID)..."
            kill $BACKEND_PID
        fi
        rm -f backend.pid
    fi
    
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if ps -p $FRONTEND_PID > /dev/null; then
            print_status "Stopping frontend (PID: $FRONTEND_PID)..."
            kill $FRONTEND_PID
        fi
        rm -f frontend.pid
    fi
    
    print_success "Services stopped successfully."
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    check_prerequisites
    start_backend
    start_frontend
    check_health
    show_services
    
    # Keep script running
    print_status "Press Ctrl+C to stop all services..."
    while true; do
        sleep 1
    done
}

# Run main function
main
