# Development Configuration with H2 Database
server:
  port: 8080
  servlet:
    context-path: /
  error:
    include-message: always
    include-binding-errors: always

spring:
  # Allow bean definition overriding for development
  main:
    allow-bean-definition-overriding: true

  # Disable OAuth2 and Security for development
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration
      - org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration

  # H2 Database Configuration for Development
  datasource:
    url: jdbc:h2:mem:banking_dev;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    hikari:
      pool-name: BankingAPI-Dev-H2-HikariCP
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000
      leak-detection-threshold: 30000

  # H2 Console Configuration
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true

  # JPA Configuration for H2
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        generate_statistics: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  # Redis Configuration (Embedded for Development)
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  # Security Configuration (Disable for Development Testing)
  security:
    oauth2:
      resourceserver:
        jwt:
          # Use mock JWT for development
          issuer-uri: http://localhost:8080/mock-keycloak/realms/banking-realm
          jwk-set-uri: http://localhost:8080/mock-keycloak/realms/banking-realm/protocol/openid-connect/certs

# Keycloak Configuration (Mock for Development)
keycloak:
  realm: banking-realm
  auth-server-url: http://localhost:8080/mock-keycloak
  resource: banking-api
  public-client: false
  bearer-only: true
  credentials:
    secret: mock-secret

# Logging Configuration
logging:
  level:
    root: INFO
    com.banking.api: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n"

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# Custom Application Properties
banking:
  api:
    cors:
      allowed-origins: http://localhost:3000,http://localhost:4200
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      max-age: 3600
    rate-limit:
      enabled: false  # Disable for development

  # Crypto Monitoring Configuration
  crypto:
    collection:
      enabled: true
      cron: "0 */1 * * * ?"  # Every minute
      symbols: "BTC,ETH,ADA,DOT,LINK,MATIC,SOL,AVAX,ATOM,NEAR"
      timeout: 30000  # 30 seconds
      max-retries: 3
      retry-delay: 15000  # 15 seconds
      batch-size: 10
    api:
      coingecko:
        url: "https://api.coingecko.com/api/v3"
        key: ""  # Free tier - no key needed
      coinmarketcap:
        url: "https://pro-api.coinmarketcap.com/v1"
        key: ""  # Add your API key here
    notification:
      price-change-threshold: 3.0  # 3% change triggers notification
      enabled: true

  # Scheduler Configuration
  scheduler:
    enabled: true
    thread-pool-size: 5
    await-termination-seconds: 60

  # Rate Limiting Configuration
  rate-limit:
    default-limit: 1000
    auth-limit: 100
    transaction-limit: 500

  # Transaction Configuration
  transaction:
    daily-limit: 10000
    monthly-limit: 100000
    max-amount-per-transaction: 5000

  # Security Configuration
  security:
      mock-auth: true  # Enable mock authentication for development


