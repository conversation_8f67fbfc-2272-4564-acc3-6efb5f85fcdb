# Quartz Scheduler Configuration
# Enterprise-grade configuration for crypto monitoring jobs

# Scheduler Configuration
org.quartz.scheduler.instanceName=BankingCryptoScheduler
org.quartz.scheduler.instanceId=AUTO
org.quartz.scheduler.skipUpdateCheck=true
org.quartz.scheduler.jmx.export=true

# Thread Pool Configuration
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount=10
org.quartz.threadPool.threadPriority=5
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true

# Job Store Configuration
# For development: RAMJobStore (in-memory)
org.quartz.jobStore.class=org.quartz.simpl.RAMJobStore

# For production: Database-backed JobStore (uncomment below and comment RAMJobStore)
# org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
# org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.oracle.OracleDelegate
# org.quartz.jobStore.useProperties=false
# org.quartz.jobStore.dataSource=quartzDataSource
# org.quartz.jobStore.tablePrefix=QRTZ_
# org.quartz.jobStore.isClustered=true
# org.quartz.jobStore.clusterCheckinInterval=20000
# org.quartz.jobStore.maxMisfiresToHandleAtATime=1
# org.quartz.jobStore.misfireThreshold=120000
# org.quartz.jobStore.txIsolationLevelSerializable=false

# Plugin Configuration
org.quartz.plugin.shutdownhook.class=org.quartz.plugins.management.ShutdownHookPlugin
org.quartz.plugin.shutdownhook.cleanShutdown=true

# Job History Plugin
org.quartz.plugin.jobHistory.class=org.quartz.plugins.history.LoggingJobHistoryPlugin
org.quartz.plugin.jobHistory.jobSuccessMessage=Job [{1}.{0}] execution complete in {2}ms
org.quartz.plugin.jobHistory.jobFailedMessage=Job [{1}.{0}] execution failed: {8}

# Triggering Configuration
org.quartz.plugin.triggHistory.class=org.quartz.plugins.history.LoggingTriggerHistoryPlugin
org.quartz.plugin.triggHistory.triggerFiredMessage=Trigger [{1}.{0}] fired job [{6}.{5}] at: {4, date, HH:mm:ss MM/dd/yyyy}
org.quartz.plugin.triggHistory.triggerCompleteMessage=Trigger [{1}.{0}] completed firing job [{6}.{5}] at {4, date, HH:mm:ss MM/dd/yyyy} with result: {9}

# Development specific settings
org.quartz.scheduler.makeSchedulerThreadDaemon=false
org.quartz.scheduler.threadsInheritContextClassLoaderOfInitializingThread=true
