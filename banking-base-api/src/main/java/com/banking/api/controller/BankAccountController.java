package com.banking.api.controller;

import com.banking.api.dto.BankAccountDto;
import com.banking.api.dto.response.ApiResponse;
import com.banking.api.entity.BankAccount;
import com.banking.api.service.BankAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * Bank Account Controller
 * Manages bank account operations
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/bank-accounts")
@Tag(name = "Bank Account Management", description = "APIs for managing bank accounts")
public class BankAccountController extends BaseController<BankAccount, BankAccountService> {

    public BankAccountController(BankAccountService bankAccountService) {
        super(bankAccountService);
    }

    @Override
    protected String getEntityName() {
        return "BankAccount";
    }

    /**
     * Get account by account number
     */
    @GetMapping("/number/{accountNumber}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    @Operation(summary = "Get account by account number", description = "Retrieve bank account by account number")
    public ResponseEntity<ApiResponse<BankAccountDto>> getByAccountNumber(
            @Parameter(description = "Account number") @PathVariable String accountNumber) {
        log.debug("Getting account by number: {}", accountNumber);
        
        BankAccountDto account = service.findByAccountNumber(accountNumber);
        
        return ResponseEntity.ok(ApiResponse.<BankAccountDto>builder()
                .success(true)
                .message("Bank account retrieved successfully")
                .data(account)
                .build());
    }

    /**
     * Get accounts by customer ID
     */
    @GetMapping("/customer/{customerId}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    @Operation(summary = "Get accounts by customer", description = "Retrieve all accounts for a customer")
    public ResponseEntity<ApiResponse<List<BankAccountDto>>> getByCustomerId(
            @Parameter(description = "Customer ID") @PathVariable Long customerId) {
        log.debug("Getting accounts for customer: {}", customerId);

        List<BankAccountDto> accounts = service.findByCustomerId(customerId);
        
        return ResponseEntity.ok(ApiResponse.<List<BankAccountDto>>builder()
                .success(true)
                .message("Customer accounts retrieved successfully")
                .data(accounts)
                .build());
    }

    /**
     * Get accounts by status
     */
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    @Operation(summary = "Get accounts by status", description = "Retrieve accounts filtered by status")
    public ResponseEntity<ApiResponse<List<BankAccountDto>>> getByStatus(
            @Parameter(description = "Account status") @PathVariable BankAccount.AccountStatus status) {
        log.debug("Getting accounts by status: {}", status);
        
        List<BankAccountDto> accounts = service.findByStatus(status);
        
        return ResponseEntity.ok(ApiResponse.<List<BankAccountDto>>builder()
                .success(true)
                .message("Accounts retrieved successfully")
                .data(accounts)
                .build());
    }

    /**
     * Get accounts by account type
     */
    @GetMapping("/type/{accountType}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    @Operation(summary = "Get accounts by type", description = "Retrieve accounts filtered by account type")
    public ResponseEntity<ApiResponse<List<BankAccountDto>>> getByAccountType(
            @Parameter(description = "Account type") @PathVariable BankAccount.AccountType accountType) {
        log.debug("Getting accounts by type: {}", accountType);
        
        List<BankAccountDto> accounts = service.findByAccountType(accountType);
        
        return ResponseEntity.ok(ApiResponse.<List<BankAccountDto>>builder()
                .success(true)
                .message("Accounts retrieved successfully")
                .data(accounts)
                .build());
    }

    /**
     * Update account status
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    @Operation(summary = "Update account status", description = "Update bank account status")
    public ResponseEntity<ApiResponse<BankAccountDto>> updateStatus(
            @Parameter(description = "Account ID") @PathVariable UUID id,
            @Parameter(description = "New status") @RequestParam BankAccount.AccountStatus status) {
        log.debug("Updating status for account: {} to {}", id, status);
        
        BankAccountDto account = service.updateStatus(id, status);
        
        return ResponseEntity.ok(ApiResponse.<BankAccountDto>builder()
                .success(true)
                .message("Account status updated successfully")
                .data(account)
                .build());
    }
} 