package com.banking.api.controller;

import com.banking.api.dto.response.ApiResponse;
import com.banking.api.scheduler.config.CryptoPriceJobConfig;
import com.banking.api.service.crypto.CryptoDataService;
import com.banking.api.service.websocket.WebSocketNotificationService;
import com.banking.api.websocket.handler.WebSocketConnectionHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.annotation.SubscribeMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

/**
 * WebSocket Controller
 * Handles WebSocket/STOMP message routing and client interactions
 * 
 * Features:
 * - Real-time crypto price subscriptions
 * - User-specific notifications
 * - Admin controls for job management
 * - Connection monitoring
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/websocket")
@RequiredArgsConstructor
public class WebSocketController {

    private final WebSocketNotificationService notificationService;
    private final CryptoDataService cryptoDataService;
    private final CryptoPriceJobConfig cryptoJobConfig;
    private final WebSocketConnectionHandler connectionHandler;

    /**
     * REST endpoint to get WebSocket connection info
     */
    @GetMapping("/info")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Map<String, Object>> getWebSocketInfo() {
        Map<String, Object> info = Map.of(
                "endpoints", Map.of(
                        "websocket", "/ws",
                        "sockjs", "/ws",
                        "native", "/ws-native"
                ),
                "topics", Map.of(
                        "market", "/topic/crypto.market",
                        "alerts", "/topic/crypto.alerts",
                        "bitcoin", "/topic/crypto.btc",
                        "ethereum", "/topic/crypto.eth"
                ),
                "userQueues", Map.of(
                        "notifications", "/user/queue/notifications",
                        "alerts", "/user/queue/alerts"
                ),
                "connectionStats", connectionHandler.getConnectionStats(),
                "messagingStats", notificationService.getMessagingStats()
        );
        
        return ApiResponse.success(info, "WebSocket information retrieved successfully");
    }

    /**
     * REST endpoint to trigger manual crypto data collection
     */
    @PostMapping("/crypto/collect")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> triggerCryptoCollection() {
        try {
            cryptoJobConfig.triggerCryptoPriceCollection();
            return ApiResponse.success("triggered", "Crypto price collection triggered successfully");
        } catch (Exception e) {
            log.error("Failed to trigger crypto collection", e);
            return ApiResponse.success(null, "Failed to trigger crypto collection: " + e.getMessage());
        }
    }

    /**
     * REST endpoint to get job status
     */
    @GetMapping("/crypto/job-status")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Map<String, Object>> getCryptoJobStatus() {
        Map<String, Object> status = cryptoJobConfig.getJobStatus();
        return ApiResponse.success(status, "Crypto job status retrieved successfully");
    }

    // STOMP Message Mappings

    /**
     * Handle crypto subscription requests
     */
    @SubscribeMapping("/crypto.{symbol}")
    public Map<String, Object> subscribeToCrypto(@DestinationVariable String symbol, Principal principal) {
        log.info("User {} subscribed to crypto: {}", principal.getName(), symbol);
        
        // Send current price data immediately upon subscription
        try {
            Set<String> symbols = Set.of(symbol.toUpperCase());
            cryptoDataService.getCachedCryptoData(symbols)
                    .take(1)
                    .subscribe(data -> {
                        notificationService.sendToUser(
                                principal.getName(),
                                "/queue/crypto." + symbol.toLowerCase(),
                                Map.of(
                                        "type", "INITIAL_DATA",
                                        "symbol", symbol.toUpperCase(),
                                        "data", data,
                                        "timestamp", LocalDateTime.now()
                                )
                        );
                    });
        } catch (Exception e) {
            log.error("Error sending initial crypto data for {}", symbol, e);
        }

        return Map.of(
                "type", "SUBSCRIPTION_CONFIRMED",
                "symbol", symbol.toUpperCase(),
                "topic", "/topic/crypto." + symbol.toLowerCase(),
                "timestamp", LocalDateTime.now()
        );
    }

    /**
     * Handle market overview subscription
     */
    @SubscribeMapping("/crypto.market")
    public Map<String, Object> subscribeToMarket(Principal principal) {
        log.info("User {} subscribed to crypto market overview", principal.getName());
        
        return Map.of(
                "type", "MARKET_SUBSCRIPTION_CONFIRMED",
                "topic", "/topic/crypto.market",
                "availableSymbols", cryptoDataService.getDefaultSymbols(),
                "timestamp", LocalDateTime.now()
        );
    }

    /**
     * Handle crypto alerts subscription
     */
    @SubscribeMapping("/crypto.alerts")
    public Map<String, Object> subscribeToAlerts(Principal principal) {
        log.info("User {} subscribed to crypto alerts", principal.getName());
        
        return Map.of(
                "type", "ALERTS_SUBSCRIPTION_CONFIRMED",
                "topic", "/topic/crypto.alerts",
                "timestamp", LocalDateTime.now()
        );
    }

    /**
     * Handle user preference updates
     */
    @MessageMapping("/crypto.preferences")
    @SendTo("/topic/crypto.preferences.updated")
    public Map<String, Object> updateCryptoPreferences(@Payload Map<String, Object> preferences, Principal principal) {
        log.info("User {} updated crypto preferences: {}", principal.getName(), preferences);
        
        // Process preferences (store in database, update subscriptions, etc.)
        // This is where you'd implement user-specific crypto tracking preferences
        
        return Map.of(
                "type", "PREFERENCES_UPDATED",
                "userId", principal.getName(),
                "preferences", preferences,
                "timestamp", LocalDateTime.now()
        );
    }

    /**
     * Handle ping/pong for connection health
     */
    @MessageMapping("/ping")
    @SendTo("/topic/pong")
    public Map<String, Object> handlePing(Principal principal) {
        log.debug("Received ping from user: {}", principal.getName());
        
        return Map.of(
                "type", "PONG",
                "userId", principal.getName(),
                "timestamp", LocalDateTime.now(),
                "serverTime", System.currentTimeMillis()
        );
    }

    /**
     * Handle admin commands
     */
    @MessageMapping("/admin.command")
    @PreAuthorize("hasRole('ADMIN')")
    public void handleAdminCommand(@Payload Map<String, Object> command, Principal principal) {
        log.info("Admin command received from {}: {}", principal.getName(), command);
        
        String action = (String) command.get("action");
        
        try {
            switch (action) {
                case "PAUSE_CRYPTO_COLLECTION":
                    cryptoJobConfig.pauseCryptoCollection();
                    notificationService.broadcastToTopic("/topic/admin.notifications", Map.of(
                            "type", "COMMAND_EXECUTED",
                            "action", action,
                            "result", "SUCCESS",
                            "executedBy", principal.getName(),
                            "timestamp", LocalDateTime.now()
                    ));
                    break;
                    
                case "RESUME_CRYPTO_COLLECTION":
                    cryptoJobConfig.resumeCryptoCollection();
                    notificationService.broadcastToTopic("/topic/admin.notifications", Map.of(
                            "type", "COMMAND_EXECUTED",
                            "action", action,
                            "result", "SUCCESS",
                            "executedBy", principal.getName(),
                            "timestamp", LocalDateTime.now()
                    ));
                    break;
                    
                case "TRIGGER_COLLECTION":
                    cryptoJobConfig.triggerCryptoPriceCollection();
                    notificationService.broadcastToTopic("/topic/admin.notifications", Map.of(
                            "type", "COMMAND_EXECUTED",
                            "action", action,
                            "result", "SUCCESS",
                            "executedBy", principal.getName(),
                            "timestamp", LocalDateTime.now()
                    ));
                    break;
                    
                default:
                    log.warn("Unknown admin command: {}", action);
            }
        } catch (Exception e) {
            log.error("Error executing admin command: {}", action, e);
            notificationService.broadcastToTopic("/topic/admin.notifications", Map.of(
                    "type", "COMMAND_FAILED",
                    "action", action,
                    "error", e.getMessage(),
                    "executedBy", principal.getName(),
                    "timestamp", LocalDateTime.now()
            ));
        }
    }

    /**
     * Get real-time statistics
     */
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Map<String, Object>> getWebSocketStats() {
        Map<String, Object> stats = Map.of(
                "connections", connectionHandler.getConnectionStats(),
                "messaging", notificationService.getMessagingStats(),
                "jobs", cryptoJobConfig.getJobStatus(),
                "timestamp", LocalDateTime.now()
        );
        
        return ApiResponse.success(stats, "WebSocket statistics retrieved successfully");
    }

    /**
     * Test endpoint to send a ping message to all connected clients
     */
    @PostMapping("/test-ping")
    public ApiResponse<Map<String, Object>> testPing(@RequestBody(required = false) Map<String, Object> payload) {
        log.info("Test ping endpoint called with payload: {}", payload);

        try {
            // Create test message
            Map<String, Object> testMessage = Map.of(
                "type", "ping",
                "message", payload != null ? payload.getOrDefault("message", "Test ping from server") : "Test ping from server",
                "timestamp", LocalDateTime.now(),
                "from", "server"
            );

            // Send to all connected clients via /topic/system/pong
            notificationService.broadcastToTopic("/topic/system/pong", testMessage);

            // Get connection stats
            Map<String, Object> stats = connectionHandler.getConnectionStats();

            Map<String, Object> result = Map.of(
                "message", "Test ping sent successfully",
                "payload", testMessage,
                "connectionStats", stats
            );

            return ApiResponse.success(result, "Test ping sent to all connected clients");

        } catch (Exception e) {
            log.error("Error sending test ping", e);
            throw new RuntimeException("Failed to send test ping: " + e.getMessage());
        }
    }
}
