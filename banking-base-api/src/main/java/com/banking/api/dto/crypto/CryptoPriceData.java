package com.banking.api.dto.crypto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Crypto Price Data DTO
 * Represents cryptocurrency price and market information
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class CryptoPriceData {

    private String symbol;
    private String name;

    @JsonProperty("price_usd")
    private BigDecimal priceUsd;

    @JsonProperty("price_btc")
    private BigDecimal priceBtc;

    @JsonProperty("market_cap_usd")
    private BigDecimal marketCapUsd;

    @JsonProperty("volume_24h_usd")
    private BigDecimal volume24hUsd;

    @JsonProperty("percent_change_24h")
    private BigDecimal percentChange24h;

    @JsonProperty("percent_change_7d")
    private BigDecimal percentChange7d;

    @JsonProperty("percent_change_30d")
    private BigDecimal percentChange30d;

    private Integer rank;

    @JsonProperty("circulating_supply")
    private BigDecimal circulatingSupply;

    @JsonProperty("total_supply")
    private BigDecimal totalSupply;

    @JsonProperty("max_supply")
    private BigDecimal maxSupply;

    private String source;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime timestamp;

    @JsonProperty("last_updated")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime lastUpdated;

    private Map<String, Object> metadata;

    public enum PriceTrend {
        BULLISH, BEARISH, SIDEWAYS, VOLATILE
    }

    public PriceTrend getPriceTrend() {
        if (percentChange24h == null) {
            return PriceTrend.SIDEWAYS;
        }

        BigDecimal change = percentChange24h.abs();

        if (change.compareTo(BigDecimal.valueOf(10)) > 0) {
            return PriceTrend.VOLATILE;
        } else if (percentChange24h.compareTo(BigDecimal.valueOf(2)) > 0) {
            return PriceTrend.BULLISH;
        } else if (percentChange24h.compareTo(BigDecimal.valueOf(-2)) < 0) {
            return PriceTrend.BEARISH;
        } else {
            return PriceTrend.SIDEWAYS;
        }
    }

    public boolean hasSignificantChange(BigDecimal threshold) {
        return percentChange24h != null &&
               percentChange24h.abs().compareTo(threshold) > 0;
    }

    public String getFormattedPrice() {
        if (priceUsd == null) {
            return "N/A";
        }

        if (priceUsd.compareTo(BigDecimal.ONE) < 0) {
            return String.format("$%.6f", priceUsd);
        } else if (priceUsd.compareTo(BigDecimal.valueOf(1000)) < 0) {
            return String.format("$%.2f", priceUsd);
        } else {
            return String.format("$%,.0f", priceUsd);
        }
    }

    public String getFormattedMarketCap() {
        if (marketCapUsd == null) {
            return "N/A";
        }

        BigDecimal billion = BigDecimal.valueOf(1_000_000_000);
        BigDecimal million = BigDecimal.valueOf(1_000_000);

        if (marketCapUsd.compareTo(billion) >= 0) {
            return String.format("$%.2fB", marketCapUsd.divide(billion));
        } else if (marketCapUsd.compareTo(million) >= 0) {
            return String.format("$%.2fM", marketCapUsd.divide(million));
        } else {
            return String.format("$%,.0f", marketCapUsd);
        }
    }

    public boolean isValid() {
        return symbol != null && !symbol.isEmpty() &&
               priceUsd != null && priceUsd.compareTo(BigDecimal.ZERO) > 0;
    }
}