package com.banking.api.scheduler.job;

import com.banking.api.dto.crypto.CryptoPriceData;
import com.banking.api.service.crypto.CryptoDataService;
import com.banking.api.service.websocket.WebSocketNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Crypto Price Collector Job
 * Scheduled job to collect cryptocurrency prices and broadcast via WebSocket
 * 
 * Features:
 * - Multi-source data collection
 * - Real-time WebSocket broadcasting
 * - Error handling and recovery
 * - Performance monitoring
 * - User-specific notifications
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Component
public class CryptoPriceCollectorJob extends AbstractScheduledJob {

    @Autowired
    private CryptoDataService cryptoDataService;

    @Autowired
    private WebSocketNotificationService notificationService;

    @Value("${banking.crypto.collection.enabled:true}")
    private boolean collectionEnabled;

    @Value("${banking.crypto.collection.symbols:BTC,ETH,ADA,DOT,LINK,MATIC,SOL,AVAX}")
    private String cryptoSymbols;

    @Value("${banking.crypto.collection.batch-size:10}")
    private int batchSize;

    @Value("${banking.crypto.notification.price-change-threshold:5.0}")
    private double priceChangeThreshold;

    @Override
    protected void doExecute(org.quartz.JobExecutionContext context, AbstractScheduledJob.JobExecutionContext executionContext)
            throws JobExecutionException {
        
        if (!collectionEnabled) {
            log.info("Crypto data collection is disabled");
            return;
        }

        try {
            log.info("Starting crypto price collection job execution");
            
            // Get symbols to collect
            Set<String> symbols = getSymbolsToCollect();
            
            if (symbols.isEmpty()) {
                log.warn("No crypto symbols configured for collection");
                return;
            }

            // Collect data from primary source
            List<CryptoPriceData> cryptoData = collectCryptoData(symbols).get(30, TimeUnit.SECONDS);
            
            if (cryptoData.isEmpty()) {
                log.warn("No crypto data collected");
                return;
            }

            // Process and broadcast data
            processCryptoData(cryptoData);
            
            // Broadcast to WebSocket subscribers
            broadcastCryptoData(cryptoData);
            
            // Send notifications for significant price changes
            sendPriceChangeNotifications(cryptoData);
            
            log.info("Successfully collected and broadcasted {} crypto prices", cryptoData.size());
            
        } catch (Exception e) {
            log.error("Error in crypto price collection job", e);
            throw new JobExecutionException("Crypto price collection failed", e);
        }
    }

    /**
     * Collect crypto data asynchronously
     */
    private CompletableFuture<List<CryptoPriceData>> collectCryptoData(Set<String> symbols) {
        return cryptoDataService.fetchCryptoData(symbols)
                .collectList()
                .toFuture();
    }

    /**
     * Process collected crypto data
     */
    private void processCryptoData(List<CryptoPriceData> cryptoData) {
        log.debug("Processing {} crypto data entries", cryptoData.size());
        
        // Add processing timestamp
        cryptoData.forEach(data -> {
            data.setTimestamp(LocalDateTime.now());
            
            // Log significant price changes
            if (data.hasSignificantChange(java.math.BigDecimal.valueOf(priceChangeThreshold))) {
                log.info("Significant price change detected: {} - {}% change", 
                        data.getSymbol(), data.getPercentChange24h());
            }
        });
        
        // Additional processing logic can be added here:
        // - Store to database
        // - Update cache
        // - Calculate technical indicators
        // - Trigger alerts
    }

    /**
     * Broadcast crypto data via WebSocket
     */
    private void broadcastCryptoData(List<CryptoPriceData> cryptoData) {
        try {
            // Broadcast to general crypto topic
            notificationService.broadcastToTopic("/topic/crypto.market", Map.of(
                    "type", "MARKET_UPDATE",
                    "data", cryptoData,
                    "timestamp", LocalDateTime.now()
            ));

            // Broadcast individual coin updates
            cryptoData.forEach(data -> {
                String topic = "/topic/crypto." + data.getSymbol().toLowerCase();
                notificationService.broadcastToTopic(topic, Map.of(
                        "type", "PRICE_UPDATE",
                        "symbol", data.getSymbol(),
                        "data", data,
                        "timestamp", LocalDateTime.now()
                ));
            });
            
            log.debug("Broadcasted crypto data to {} WebSocket topics", cryptoData.size() + 1);
            
        } catch (Exception e) {
            log.error("Error broadcasting crypto data via WebSocket", e);
        }
    }

    /**
     * Send notifications for significant price changes
     */
    private void sendPriceChangeNotifications(List<CryptoPriceData> cryptoData) {
        cryptoData.stream()
                .filter(data -> data.hasSignificantChange(java.math.BigDecimal.valueOf(priceChangeThreshold)))
                .forEach(data -> {
                    try {
                        // Send to users who are subscribed to this crypto
                        String message = String.format(
                                "%s price changed by %.2f%% to %s",
                                data.getSymbol(),
                                data.getPercentChange24h(),
                                data.getFormattedPrice()
                        );

                        // Broadcast alert to all users (can be made user-specific)
                        notificationService.broadcastToTopic("/topic/crypto.alerts", Map.of(
                                "type", "PRICE_ALERT",
                                "symbol", data.getSymbol(),
                                "message", message,
                                "data", data,
                                "severity", getSeverityLevel(data.getPercentChange24h()),
                                "timestamp", LocalDateTime.now()
                        ));
                        
                    } catch (Exception e) {
                        log.error("Error sending price change notification for {}", data.getSymbol(), e);
                    }
                });
    }

    /**
     * Get symbols to collect from configuration
     */
    private Set<String> getSymbolsToCollect() {
        if (cryptoSymbols == null || cryptoSymbols.trim().isEmpty()) {
            return cryptoDataService.getDefaultSymbols();
        }
        return Set.of(cryptoSymbols.split(","));
    }

    /**
     * Get severity level based on price change percentage
     */
    private String getSeverityLevel(java.math.BigDecimal percentChange) {
        if (percentChange == null) {
            return "INFO";
        }
        
        java.math.BigDecimal absChange = percentChange.abs();
        
        if (absChange.compareTo(java.math.BigDecimal.valueOf(20)) >= 0) {
            return "CRITICAL";
        } else if (absChange.compareTo(java.math.BigDecimal.valueOf(10)) >= 0) {
            return "HIGH";
        } else if (absChange.compareTo(java.math.BigDecimal.valueOf(5)) >= 0) {
            return "MEDIUM";
        } else {
            return "LOW";
        }
    }

    @Override
    protected boolean shouldExecute(org.quartz.JobExecutionContext context) {
        // Check if collection is enabled and within business hours if needed
        return collectionEnabled && isMarketOpen();
    }

    /**
     * Check if crypto market is open (24/7 for crypto, but can be customized)
     */
    private boolean isMarketOpen() {
        // Crypto markets are always open, but you can add custom logic here
        // For example, pause during maintenance windows
        return true;
    }

    @Override
    protected void onJobError(AbstractScheduledJob.JobExecutionContext executionContext, Exception e) {
        super.onJobError(executionContext, e);

        // Send error notification to administrators
        try {
            notificationService.broadcastToTopic("/topic/admin.alerts", Map.of(
                    "type", "JOB_ERROR",
                    "jobName", executionContext.getJobName(),
                    "error", e.getMessage(),
                    "timestamp", LocalDateTime.now()
            ));
        } catch (Exception notificationError) {
            log.error("Failed to send job error notification", notificationError);
        }
    }

    @Override
    protected void onJobCompleted(AbstractScheduledJob.JobExecutionContext executionContext) {
        super.onJobCompleted(executionContext);

        // Log job completion metrics
        log.info("Crypto price collection job completed - Duration: {}ms, Result: {}",
                executionContext.getExecutionTimeMs(),
                executionContext.getResult());
    }
}
