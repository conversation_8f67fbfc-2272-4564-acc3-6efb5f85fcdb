package com.banking.api.scheduler.config;

import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;
import java.util.TimeZone;

/**
 * Abstract base configuration for Quartz Jobs
 * Provides common functionality for job scheduling, error handling, and monitoring
 * 
 * Design Patterns:
 * - Template Method Pattern: Define common job lifecycle
 * - Strategy Pattern: Allow different execution strategies
 * - Observer Pattern: Job execution monitoring
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public abstract class AbstractJobConfig {

    @Autowired
    protected SchedulerFactoryBean schedulerFactoryBean;
    
    @Autowired
    protected ApplicationContext applicationContext;

    /**
     * Job execution strategy enum
     */
    public enum ExecutionStrategy {
        IMMEDIATE,
        SCHEDULED,
        CONDITIONAL,
        RETRY_ON_FAILURE
    }

    /**
     * Job priority levels
     */
    public enum JobPriority {
        LOW(1),
        NORMAL(5),
        HIGH(8),
        CRITICAL(10);

        private final int value;

        JobPriority(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * Initialize job configuration after bean creation
     */
    @PostConstruct
    public void initializeJobs() {
        try {
            log.info("Initializing jobs for: {}", this.getClass().getSimpleName());
            configureJobs();
            log.info("Successfully initialized jobs for: {}", this.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("Failed to initialize jobs for: {}", this.getClass().getSimpleName(), e);
            throw new RuntimeException("Job initialization failed", e);
        }
    }

    /**
     * Template method for job configuration
     * Subclasses must implement this method to define their specific jobs
     */
    protected abstract void configureJobs() throws SchedulerException;

    /**
     * Get the job class that this configuration manages
     */
    protected abstract Class<? extends QuartzJobBean> getJobClass();

    /**
     * Get the job group name
     */
    protected abstract String getJobGroup();

    /**
     * Get the default cron expression
     */
    protected abstract String getDefaultCronExpression();

    /**
     * Get job execution strategy
     */
    protected ExecutionStrategy getExecutionStrategy() {
        return ExecutionStrategy.SCHEDULED;
    }

    /**
     * Get job priority
     */
    protected JobPriority getJobPriority() {
        return JobPriority.NORMAL;
    }

    /**
     * Get job timeout in milliseconds
     */
    protected long getJobTimeoutMs() {
        return 300000; // 5 minutes default
    }

    /**
     * Get maximum retry attempts
     */
    protected int getMaxRetryAttempts() {
        return 3;
    }

    /**
     * Get retry delay in milliseconds
     */
    protected long getRetryDelayMs() {
        return 60000; // 1 minute default
    }

    /**
     * Check if job should be enabled
     */
    protected boolean isJobEnabled() {
        return true;
    }

    /**
     * Get additional job data map
     */
    protected Map<String, Object> getJobDataMap() {
        return Map.of();
    }

    /**
     * Create and schedule a job with the given parameters
     */
    protected void createAndScheduleJob(String jobName, String cronExpression) throws SchedulerException {
        createAndScheduleJob(jobName, cronExpression, getJobDataMap());
    }

    /**
     * Create and schedule a job with custom data map
     */
    protected void createAndScheduleJob(String jobName, String cronExpression, Map<String, Object> jobDataMap) 
            throws SchedulerException {
        
        if (!isJobEnabled()) {
            log.info("Job {} is disabled, skipping creation", jobName);
            return;
        }

        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        
        // Check if job already exists
        JobKey jobKey = JobKey.jobKey(jobName, getJobGroup());
        if (scheduler.checkExists(jobKey)) {
            log.info("Job {} already exists, updating...", jobName);
            scheduler.deleteJob(jobKey);
        }

        // Create job detail
        JobDetail jobDetail = createJobDetail(jobName, jobDataMap);
        
        // Create trigger
        Trigger trigger = createCronTrigger(jobName, cronExpression);
        
        // Schedule job
        scheduler.scheduleJob(jobDetail, trigger);
        
        log.info("Successfully scheduled job: {} with cron: {} in group: {}", 
                jobName, cronExpression, getJobGroup());
    }

    /**
     * Create job detail with common configuration
     */
    protected JobDetail createJobDetail(String jobName, Map<String, Object> jobDataMap) {
        JobDataMap dataMap = new JobDataMap();
        dataMap.putAll(jobDataMap);
        
        // Add common job data
        dataMap.put("jobGroup", getJobGroup());
        dataMap.put("executionStrategy", getExecutionStrategy().name());
        dataMap.put("priority", getJobPriority().getValue());
        dataMap.put("timeoutMs", getJobTimeoutMs());
        dataMap.put("maxRetryAttempts", getMaxRetryAttempts());
        dataMap.put("retryDelayMs", getRetryDelayMs());
        dataMap.put("createdAt", LocalDateTime.now());

        return JobBuilder.newJob(getJobClass())
                .withIdentity(jobName, getJobGroup())
                .withDescription(getJobDescription(jobName))
                .usingJobData(dataMap)
                .storeDurably(true)
                .requestRecovery(true)
                .build();
    }

    /**
     * Create cron trigger with timezone support
     */
    protected Trigger createCronTrigger(String jobName, String cronExpression) {
        return TriggerBuilder.newTrigger()
                .withIdentity(jobName + "_trigger", getJobGroup())
                .withDescription("Trigger for " + jobName)
                .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression)
                        .inTimeZone(TimeZone.getTimeZone(ZoneId.systemDefault()))
                        .withMisfireHandlingInstructionDoNothing())
                .startAt(getJobStartTime())
                .build();
    }

    /**
     * Get job start time (default: now + 30 seconds)
     */
    protected Date getJobStartTime() {
        return Date.from(LocalDateTime.now().plusSeconds(30)
                .atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * Get job description
     */
    protected String getJobDescription(String jobName) {
        return String.format("%s job in %s group", jobName, getJobGroup());
    }

    /**
     * Create simple trigger for immediate or delayed execution
     */
    protected Trigger createSimpleTrigger(String jobName, int intervalInSeconds, int repeatCount) {
        return TriggerBuilder.newTrigger()
                .withIdentity(jobName + "_simple_trigger", getJobGroup())
                .withDescription("Simple trigger for " + jobName)
                .startNow()
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInSeconds(intervalInSeconds)
                        .withRepeatCount(repeatCount)
                        .withMisfireHandlingInstructionFireNow())
                .build();
    }

    /**
     * Pause a job
     */
    protected void pauseJob(String jobName) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = JobKey.jobKey(jobName, getJobGroup());
        scheduler.pauseJob(jobKey);
        log.info("Paused job: {} in group: {}", jobName, getJobGroup());
    }

    /**
     * Resume a job
     */
    protected void resumeJob(String jobName) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = JobKey.jobKey(jobName, getJobGroup());
        scheduler.resumeJob(jobKey);
        log.info("Resumed job: {} in group: {}", jobName, getJobGroup());
    }

    /**
     * Delete a job
     */
    protected void deleteJob(String jobName) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = JobKey.jobKey(jobName, getJobGroup());
        scheduler.deleteJob(jobKey);
        log.info("Deleted job: {} in group: {}", jobName, getJobGroup());
    }

    /**
     * Check if job exists
     */
    protected boolean jobExists(String jobName) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = JobKey.jobKey(jobName, getJobGroup());
        return scheduler.checkExists(jobKey);
    }

    /**
     * Get job execution status
     */
    protected boolean isJobRunning(String jobName) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = JobKey.jobKey(jobName, getJobGroup());
        return !scheduler.getCurrentlyExecutingJobs().stream()
                .filter(context -> context.getJobDetail().getKey().equals(jobKey))
                .toList().isEmpty();
    }
}
