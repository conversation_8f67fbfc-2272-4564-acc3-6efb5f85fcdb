package com.banking.api.scheduler.job;

import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Abstract base class for all scheduled jobs
 * Provides common functionality for job execution, error handling, monitoring, and retry logic
 * 
 * Design Patterns:
 * - Template Method Pattern: Define job execution lifecycle
 * - Strategy Pattern: Different execution strategies
 * - Circuit Breaker Pattern: Prevent cascading failures
 * - Retry Pattern: Automatic retry with exponential backoff
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public abstract class AbstractScheduledJob extends QuartzJobBean {

    @Autowired
    protected ApplicationContext applicationContext;

    private static final String EXECUTION_COUNT_KEY = "executionCount";
    private static final String LAST_EXECUTION_KEY = "lastExecution";
    private static final String LAST_SUCCESS_KEY = "lastSuccess";
    private static final String CONSECUTIVE_FAILURES_KEY = "consecutiveFailures";

    /**
     * Job execution result
     */
    public enum JobExecutionResult {
        SUCCESS,
        FAILURE,
        SKIPPED,
        TIMEOUT,
        RETRY_NEEDED
    }

    /**
     * Job execution context with metrics
     */
    public static class JobExecutionContext {
        private final String jobName;
        private final String jobGroup;
        private final LocalDateTime startTime;
        private LocalDateTime endTime;
        private JobExecutionResult result;
        private String errorMessage;
        private long executionTimeMs;
        private int retryAttempt;

        public JobExecutionContext(String jobName, String jobGroup) {
            this.jobName = jobName;
            this.jobGroup = jobGroup;
            this.startTime = LocalDateTime.now();
            this.retryAttempt = 0;
        }

        // Getters and setters
        public String getJobName() { return jobName; }
        public String getJobGroup() { return jobGroup; }
        public LocalDateTime getStartTime() { return startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public JobExecutionResult getResult() { return result; }
        public String getErrorMessage() { return errorMessage; }
        public long getExecutionTimeMs() { return executionTimeMs; }
        public int getRetryAttempt() { return retryAttempt; }

        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        public void setResult(JobExecutionResult result) { this.result = result; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public void setExecutionTimeMs(long executionTimeMs) { this.executionTimeMs = executionTimeMs; }
        public void setRetryAttempt(int retryAttempt) { this.retryAttempt = retryAttempt; }
    }

    @Override
    protected final void executeInternal(org.quartz.JobExecutionContext quartzContext) throws JobExecutionException {
        JobExecutionContext executionContext = createExecutionContext(quartzContext);
        
        try {
            // Pre-execution checks
            if (!shouldExecute(quartzContext)) {
                executionContext.setResult(JobExecutionResult.SKIPPED);
                onJobSkipped(executionContext);
                return;
            }

            // Update execution metrics
            updateExecutionMetrics(quartzContext);

            // Execute with timeout
            executeWithTimeout(quartzContext, executionContext);

        } catch (Exception e) {
            handleJobException(quartzContext, executionContext, e);
        } finally {
            // Post-execution cleanup
            finalizeExecution(executionContext);
        }
    }

    /**
     * Create execution context from Quartz context
     */
    private JobExecutionContext createExecutionContext(org.quartz.JobExecutionContext quartzContext) {
        org.quartz.JobDetail jobDetail = quartzContext.getJobDetail();
        return new JobExecutionContext(
                jobDetail.getKey().getName(),
                jobDetail.getKey().getGroup()
        );
    }

    /**
     * Execute job with timeout protection
     */
    private void executeWithTimeout(org.quartz.JobExecutionContext quartzContext, JobExecutionContext executionContext)
            throws JobExecutionException {
        
        long timeoutMs = getJobTimeoutMs(quartzContext);
        
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                doExecute(quartzContext, executionContext);
                executionContext.setResult(JobExecutionResult.SUCCESS);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        try {
            future.get(timeoutMs, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            future.cancel(true);
            executionContext.setResult(JobExecutionResult.TIMEOUT);
            executionContext.setErrorMessage("Job execution timed out after " + timeoutMs + "ms");
            throw new JobExecutionException("Job execution timed out", e);
        } catch (Exception e) {
            executionContext.setResult(JobExecutionResult.FAILURE);
            executionContext.setErrorMessage(e.getMessage());
            throw new JobExecutionException("Job execution failed", e);
        }
    }

    /**
     * Handle job execution exceptions with retry logic
     */
    private void handleJobException(org.quartz.JobExecutionContext quartzContext, JobExecutionContext executionContext, Exception e)
            throws JobExecutionException {
        
        int consecutiveFailures = getConsecutiveFailures(quartzContext) + 1;
        int maxRetryAttempts = getMaxRetryAttempts(quartzContext);
        
        executionContext.setResult(JobExecutionResult.FAILURE);
        executionContext.setErrorMessage(e.getMessage());
        
        log.error("Job execution failed: {} (attempt {}/{})", 
                executionContext.getJobName(), consecutiveFailures, maxRetryAttempts, e);

        // Update failure count
        quartzContext.getJobDetail().getJobDataMap().put(CONSECUTIVE_FAILURES_KEY, consecutiveFailures);

        // Determine if retry is needed
        if (consecutiveFailures < maxRetryAttempts && shouldRetry(quartzContext, e)) {
            executionContext.setResult(JobExecutionResult.RETRY_NEEDED);
            scheduleRetry(quartzContext, consecutiveFailures);
        } else {
            // Max retries reached, handle permanent failure
            onMaxRetriesReached(quartzContext, executionContext, e);
        }

        // Call error handler
        onJobError(executionContext, e);
    }

    /**
     * Schedule retry with exponential backoff
     */
    private void scheduleRetry(org.quartz.JobExecutionContext context, int attemptNumber) {
        long baseDelayMs = getRetryDelayMs(context);
        long delayMs = baseDelayMs * (long) Math.pow(2, attemptNumber - 1); // Exponential backoff

        log.info("Scheduling retry for job: {} in {}ms (attempt {})",
                context.getJobDetail().getKey().getName(), delayMs, attemptNumber);

        // Implementation would schedule a retry job here
        // This is a simplified version - in production, you'd use Quartz's retry mechanisms
    }

    /**
     * Update job execution metrics
     */
    private void updateExecutionMetrics(org.quartz.JobExecutionContext context) {
        org.quartz.JobDataMap dataMap = context.getJobDetail().getJobDataMap();

        int executionCount = dataMap.getIntValue(EXECUTION_COUNT_KEY) + 1;
        dataMap.put(EXECUTION_COUNT_KEY, executionCount);
        dataMap.put(LAST_EXECUTION_KEY, LocalDateTime.now());

        log.debug("Job execution #{} for: {}", executionCount, context.getJobDetail().getKey().getName());
    }

    /**
     * Finalize execution and update metrics
     */
    private void finalizeExecution(JobExecutionContext executionContext) {
        executionContext.setEndTime(LocalDateTime.now());
        executionContext.setExecutionTimeMs(
                ChronoUnit.MILLIS.between(executionContext.getStartTime(), executionContext.getEndTime())
        );

        // Log execution summary
        log.info("Job execution completed: {} - Result: {} - Duration: {}ms",
                executionContext.getJobName(),
                executionContext.getResult(),
                executionContext.getExecutionTimeMs());

        // Call completion handler
        onJobCompleted(executionContext);
    }

    // Abstract methods that subclasses must implement

    /**
     * Main job execution logic - implemented by subclasses
     */
    protected abstract void doExecute(org.quartz.JobExecutionContext context, JobExecutionContext executionContext)
            throws JobExecutionException;

    // Hook methods with default implementations

    /**
     * Check if job should execute (can be overridden for conditional execution)
     */
    protected boolean shouldExecute(org.quartz.JobExecutionContext context) {
        return true;
    }

    /**
     * Check if job should retry after failure
     */
    protected boolean shouldRetry(org.quartz.JobExecutionContext context, Exception e) {
        return true; // Default: always retry
    }

    /**
     * Called when job is skipped
     */
    protected void onJobSkipped(JobExecutionContext executionContext) {
        log.info("Job skipped: {}", executionContext.getJobName());
    }

    /**
     * Called when job encounters an error
     */
    protected void onJobError(JobExecutionContext executionContext, Exception e) {
        log.error("Job error: {} - {}", executionContext.getJobName(), e.getMessage());
    }

    /**
     * Called when max retries are reached
     */
    protected void onMaxRetriesReached(org.quartz.JobExecutionContext context, JobExecutionContext executionContext, Exception e) {
        log.error("Max retries reached for job: {} - Giving up", executionContext.getJobName());
    }

    /**
     * Called when job execution is completed (success or failure)
     */
    protected void onJobCompleted(JobExecutionContext executionContext) {
        // Default: do nothing - subclasses can override
    }

    // Utility methods for getting job configuration

    protected long getJobTimeoutMs(org.quartz.JobExecutionContext context) {
        return context.getJobDetail().getJobDataMap().getLongValue("timeoutMs");
    }

    protected int getMaxRetryAttempts(org.quartz.JobExecutionContext context) {
        return context.getJobDetail().getJobDataMap().getIntValue("maxRetryAttempts");
    }

    protected long getRetryDelayMs(org.quartz.JobExecutionContext context) {
        return context.getJobDetail().getJobDataMap().getLongValue("retryDelayMs");
    }

    protected int getConsecutiveFailures(org.quartz.JobExecutionContext context) {
        return context.getJobDetail().getJobDataMap().getIntValue(CONSECUTIVE_FAILURES_KEY);
    }

    protected void resetConsecutiveFailures(org.quartz.JobExecutionContext context) {
        context.getJobDetail().getJobDataMap().put(CONSECUTIVE_FAILURES_KEY, 0);
        context.getJobDetail().getJobDataMap().put(LAST_SUCCESS_KEY, LocalDateTime.now());
    }
}
