package com.banking.api.config;

import com.banking.api.websocket.interceptor.WebSocketAuthInterceptor;
import com.banking.api.websocket.handler.WebSocketConnectionHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.*;

/**
 * WebSocket Configuration for Real-time Communication
 * Enterprise-grade STOMP configuration with security, monitoring, and scalability
 * 
 * Features:
 * - STOMP protocol support
 * - Authentication and authorization
 * - Message broker configuration
 * - Connection management
 * - Error handling
 * - Performance optimization
 * 
 * Architecture:
 * - Simple Broker for development
 * - External Message Broker (RabbitMQ/Redis) for production
 * - Topic-based messaging
 * - User-specific subscriptions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Configuration
@EnableWebSocket
@EnableWebSocketMessageBroker
@RequiredArgsConstructor
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    private final WebSocketAuthInterceptor authInterceptor;
    private final WebSocketConnectionHandler connectionHandler;

    /**
     * Configure STOMP message broker
     * 
     * Destinations:
     * - /topic/crypto.{symbol} - Public crypto price updates
     * - /topic/crypto.market - General market updates
     * - /user/queue/notifications - User-specific notifications
     * - /user/queue/alerts - User-specific alerts
     */
    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // Enable simple broker for topics and queues
        config.enableSimpleBroker(
                "/topic",    // Public broadcasts
                "/queue"     // User-specific messages
        )
        .setHeartbeatValue(new long[]{10000, 10000}) // 10 seconds heartbeat
        .setTaskScheduler(taskScheduler());

        // Set application destination prefix
        config.setApplicationDestinationPrefixes("/app");
        
        // Set user destination prefix
        config.setUserDestinationPrefix("/user");
        
        // Configure message size limits
        config.setCacheLimit(1024);
        
        log.info("Configured STOMP message broker with topics and user queues");
    }

    /**
     * Register STOMP endpoints
     */
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // Main WebSocket endpoint with SockJS fallback
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*") // Configure properly for production
                .addInterceptors(connectionHandler)
                .withSockJS()
                .setHeartbeatTime(25000) // 25 seconds
                .setDisconnectDelay(30000) // 30 seconds
                .setStreamBytesLimit(128 * 1024) // 128KB
                .setHttpMessageCacheSize(1000)
                .setClientLibraryUrl("https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js");

        // Native WebSocket endpoint (for modern browsers)
        registry.addEndpoint("/ws-native")
                .setAllowedOriginPatterns("*")
                .addInterceptors(connectionHandler);
                
        log.info("Registered STOMP endpoints: /ws (with SockJS) and /ws-native");
    }

    /**
     * Configure client inbound channel
     */
    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        // Add authentication interceptor
        registration.interceptors(authInterceptor);
        
        // Configure thread pool for inbound messages
        registration.taskExecutor()
                .corePoolSize(4)
                .maxPoolSize(8)
                .queueCapacity(100)
                .keepAliveSeconds(60);
                
        log.debug("Configured client inbound channel with authentication and thread pool");
    }

    /**
     * Configure client outbound channel
     */
    @Override
    public void configureClientOutboundChannel(ChannelRegistration registration) {
        // Configure thread pool for outbound messages
        registration.taskExecutor()
                .corePoolSize(4)
                .maxPoolSize(8)
                .queueCapacity(100)
                .keepAliveSeconds(60);
                
        log.debug("Configured client outbound channel with thread pool");
    }

    /**
     * Task scheduler for WebSocket heartbeat and maintenance
     */
    @Bean
    public org.springframework.scheduling.TaskScheduler taskScheduler() {
        org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler scheduler = 
                new org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler();
        scheduler.setPoolSize(2);
        scheduler.setThreadNamePrefix("websocket-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(60);
        return scheduler;
    }
}
