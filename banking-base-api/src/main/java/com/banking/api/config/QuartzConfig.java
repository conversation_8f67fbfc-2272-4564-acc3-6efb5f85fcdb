package com.banking.api.config;

import lombok.extern.slf4j.Slf4j;
import org.quartz.spi.JobFactory;
import org.quartz.spi.TriggerFiredBundle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.EnableScheduling;

import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.scheduling.quartz.SpringBeanJobFactory;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Properties;

/**
 * Quartz Scheduler Configuration
 * Enterprise-grade job scheduling with clustering support, persistence, and monitoring
 * 
 * Features:
 * - Database-backed job persistence
 * - Cluster-aware scheduling
 * - Spring integration
 * - Comprehensive monitoring
 * - Graceful shutdown
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Configuration
@EnableScheduling
@ConditionalOnProperty(name = "banking.scheduler.enabled", havingValue = "true", matchIfMissing = true)
public class QuartzConfig {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * Spring-aware job factory for dependency injection
     */
    @Bean
    public JobFactory jobFactory() {
        AutowiringSpringBeanJobFactory jobFactory = new AutowiringSpringBeanJobFactory();
        jobFactory.setApplicationContext(applicationContext);
        return jobFactory;
    }

    /**
     * Main scheduler factory bean with enterprise configuration
     */
    @Bean
    public SchedulerFactoryBean schedulerFactoryBean() throws IOException {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        
        // Set job factory for Spring integration
        factory.setJobFactory(jobFactory());
        
        // Set data source for job persistence
        factory.setDataSource(dataSource);
        
        // Set Quartz properties
        factory.setQuartzProperties(quartzProperties());
        
        // Set scheduler name
        factory.setSchedulerName("BankingCryptoScheduler");
        
        // Auto-start scheduler
        factory.setAutoStartup(true);
        
        // Wait for jobs to complete on shutdown
        factory.setWaitForJobsToCompleteOnShutdown(true);
        
        // Startup delay to ensure all beans are initialized
        factory.setStartupDelay(30);
        
        // Override existing jobs
        factory.setOverwriteExistingJobs(true);
        
        log.info("Configured Quartz Scheduler with database persistence and clustering support");
        
        return factory;
    }

    /**
     * Quartz properties for enterprise configuration
     */
    @Bean
    public Properties quartzProperties() throws IOException {
        PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
        propertiesFactoryBean.setLocation(new ClassPathResource("quartz.properties"));
        propertiesFactoryBean.afterPropertiesSet();
        
        Properties properties = propertiesFactoryBean.getObject();
        if (properties == null) {
            properties = getDefaultQuartzProperties();
        }
        
        return properties;
    }

    /**
     * Default Quartz properties if quartz.properties file is not found
     */
    private Properties getDefaultQuartzProperties() {
        Properties properties = new Properties();
        
        // Scheduler Configuration
        properties.setProperty("org.quartz.scheduler.instanceName", "BankingCryptoScheduler");
        properties.setProperty("org.quartz.scheduler.instanceId", "AUTO");
        properties.setProperty("org.quartz.scheduler.skipUpdateCheck", "true");
        properties.setProperty("org.quartz.scheduler.jmx.export", "true");
        
        // Thread Pool Configuration
        properties.setProperty("org.quartz.threadPool.class", "org.quartz.simpl.SimpleThreadPool");
        properties.setProperty("org.quartz.threadPool.threadCount", "10");
        properties.setProperty("org.quartz.threadPool.threadPriority", "5");
        properties.setProperty("org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread", "true");
        
        // Job Store Configuration (Database)
        properties.setProperty("org.quartz.jobStore.class", "org.quartz.impl.jdbcjobstore.JobStoreTX");
        properties.setProperty("org.quartz.jobStore.driverDelegateClass", "org.quartz.impl.jdbcjobstore.oracle.OracleDelegate");
        properties.setProperty("org.quartz.jobStore.useProperties", "false");
        properties.setProperty("org.quartz.jobStore.dataSource", "quartzDataSource");
        properties.setProperty("org.quartz.jobStore.tablePrefix", "QRTZ_");
        properties.setProperty("org.quartz.jobStore.isClustered", "true");
        properties.setProperty("org.quartz.jobStore.clusterCheckinInterval", "20000");
        properties.setProperty("org.quartz.jobStore.maxMisfiresToHandleAtATime", "1");
        properties.setProperty("org.quartz.jobStore.misfireThreshold", "120000");
        properties.setProperty("org.quartz.jobStore.txIsolationLevelSerializable", "false");
        
        // Plugin Configuration
        properties.setProperty("org.quartz.plugin.shutdownhook.class", "org.quartz.plugins.management.ShutdownHookPlugin");
        properties.setProperty("org.quartz.plugin.shutdownhook.cleanShutdown", "true");
        
        // Logging Configuration
        properties.setProperty("org.quartz.plugin.jobHistory.class", "org.quartz.plugins.history.LoggingJobHistoryPlugin");
        properties.setProperty("org.quartz.plugin.jobHistory.jobSuccessMessage", "Job [{1}.{0}] execution complete in {2}ms");
        properties.setProperty("org.quartz.plugin.jobHistory.jobFailedMessage", "Job [{1}.{0}] execution failed: {8}");
        
        return properties;
    }

    /**
     * Custom Spring Bean Job Factory with autowiring support
     */
    public static class AutowiringSpringBeanJobFactory extends SpringBeanJobFactory {
        
        private ApplicationContext applicationContext;

        public void setApplicationContext(ApplicationContext applicationContext) {
            this.applicationContext = applicationContext;
        }

        @Override
        protected Object createJobInstance(TriggerFiredBundle bundle) throws Exception {
            Object job = super.createJobInstance(bundle);
            
            // Autowire the job instance
            if (applicationContext != null) {
                applicationContext.getAutowireCapableBeanFactory().autowireBean(job);
            }
            
            return job;
        }
    }


}
