package com.banking.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Mock Security Configuration for Development
 * Provides simple authentication for testing without Keycloak
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true, jsr250Enabled = true)
@Profile("dev")
@ConditionalOnProperty(name = "banking.api.security.mock-auth", havingValue = "true")
public class MockSecurityConfig {

    /**
     * Configure Security Filter Chain for Development
     */
    @Bean
    @Primary
    public SecurityFilterChain mockSecurityFilterChain(HttpSecurity http) throws Exception {
        log.info("Configuring Mock Security Filter Chain for Development");

        http
            // Disable CSRF for development
            .csrf(AbstractHttpConfigurer::disable)
            
            // Configure CORS
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            
            // Configure session management
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )
            
            // Configure authorization rules - ALLOW ALL for development
            .authorizeHttpRequests(authz -> authz
                .anyRequest().permitAll()
            )
            
            // Configure HTTP Basic authentication for development
            .httpBasic(httpBasic -> httpBasic.realmName("Banking API Development"))
            
            // Configure headers for H2 console
            .headers(headers -> headers
                .frameOptions(frameOptions -> frameOptions.sameOrigin())
                .contentTypeOptions(contentTypeOptions -> contentTypeOptions.disable())
            );

        log.info("Mock Security Filter Chain configured successfully");
        return http.build();
    }

    /**
     * In-memory user details service for development
     */
    @Bean
    @Primary
    public UserDetailsService mockUserDetailsService() {
        log.info("Creating mock users for development");
        
        UserDetails admin = User.builder()
                .username("admin")
                .password(passwordEncoder().encode("admin123"))
                .authorities(
                    new SimpleGrantedAuthority("ROLE_ADMIN"),
                    new SimpleGrantedAuthority("ROLE_USER"),
                    new SimpleGrantedAuthority("ROLE_MANAGER")
                )
                .build();

        UserDetails manager = User.builder()
                .username("manager")
                .password(passwordEncoder().encode("manager123"))
                .authorities(
                    new SimpleGrantedAuthority("ROLE_MANAGER"),
                    new SimpleGrantedAuthority("ROLE_USER")
                )
                .build();

        UserDetails user = User.builder()
                .username("user")
                .password(passwordEncoder().encode("user123"))
                .authorities(new SimpleGrantedAuthority("ROLE_USER"))
                .build();

        UserDetails accountManager = User.builder()
                .username("account_manager")
                .password(passwordEncoder().encode("account123"))
                .authorities(
                    new SimpleGrantedAuthority("ROLE_ACCOUNT_MANAGER"),
                    new SimpleGrantedAuthority("ROLE_USER")
                )
                .build();

        UserDetails transactionManager = User.builder()
                .username("transaction_manager")
                .password(passwordEncoder().encode("transaction123"))
                .authorities(
                    new SimpleGrantedAuthority("ROLE_TRANSACTION_MANAGER"),
                    new SimpleGrantedAuthority("ROLE_USER")
                )
                .build();

        log.info("Mock users created: admin, manager, user, account_manager, transaction_manager");
        
        return new InMemoryUserDetailsManager(admin, manager, user, accountManager, transactionManager);
    }

    /**
     * Password encoder for development
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * Authentication manager for development
     */
    @Bean
    public AuthenticationManager authenticationManager(
            UserDetailsService userDetailsService,
            PasswordEncoder passwordEncoder) {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder);
        return new ProviderManager(authProvider);
    }

    /**
     * Configure CORS for development
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        log.info("Configuring CORS for development");
        
        CorsConfiguration configuration = new CorsConfiguration();
        
        // Allow all origins for development
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        
        // Allow all HTTP methods
        configuration.setAllowedMethods(Arrays.asList(
            HttpMethod.GET.name(),
            HttpMethod.POST.name(),
            HttpMethod.PUT.name(),
            HttpMethod.PATCH.name(),
            HttpMethod.DELETE.name(),
            HttpMethod.OPTIONS.name(),
            HttpMethod.HEAD.name()
        ));
        
        // Allow all headers
        configuration.setAllowedHeaders(Arrays.asList("*"));
        
        // Allow credentials
        configuration.setAllowCredentials(true);
        
        // Cache preflight response
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        log.info("CORS configuration completed for development");
        return source;
    }
}
