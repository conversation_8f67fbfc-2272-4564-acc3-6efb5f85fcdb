package com.banking.api.websocket.interceptor;

import com.banking.api.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.security.Principal;
import java.util.List;
import java.util.Map;

/**
 * WebSocket Authentication Interceptor
 * Handles authentication for STOMP connections
 * 
 * Features:
 * - Token-based authentication
 * - User session management
 * - Connection authorization
 * - Security context setup
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Component
public class WebSocketAuthInterceptor implements ChannelInterceptor {

    private static final String AUTH_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String USER_ID_HEADER = "X-User-Id";
    private static final String SESSION_ID_HEADER = "X-Session-Id";

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (accessor != null) {
            StompCommand command = accessor.getCommand();
            
            if (StompCommand.CONNECT.equals(command)) {
                handleConnect(accessor);
            } else if (StompCommand.SUBSCRIBE.equals(command)) {
                handleSubscribe(accessor);
            } else if (StompCommand.SEND.equals(command)) {
                handleSend(accessor);
            } else if (StompCommand.DISCONNECT.equals(command)) {
                handleDisconnect(accessor);
            }
        }
        
        return message;
    }

    /**
     * Handle CONNECT command - authenticate user
     */
    private void handleConnect(StompHeaderAccessor accessor) {
        try {
            // Extract authentication token
            String token = extractToken(accessor);
            String userId = extractUserId(accessor);
            String sessionId = extractSessionId(accessor);
            
            log.info("WebSocket CONNECT attempt - UserId: {}, SessionId: {}", userId, sessionId);
            
            // Authenticate user
            Authentication authentication = authenticateUser(token, userId);
            
            if (authentication != null) {
                // Set user principal
                accessor.setUser(authentication);
                
                // Store user information in session attributes
                Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
                if (sessionAttributes != null) {
                    sessionAttributes.put("userId", userId);
                    sessionAttributes.put("sessionId", sessionId);
                    sessionAttributes.put("connectedAt", System.currentTimeMillis());
                    sessionAttributes.put("authenticated", true);
                }
                
                log.info("WebSocket connection authenticated for user: {}", userId);
            } else {
                log.warn("WebSocket connection failed authentication for user: {}", userId);
                throw new SecurityException("Authentication failed");
            }
            
        } catch (Exception e) {
            log.error("WebSocket authentication error", e);
            throw new SecurityException("WebSocket authentication failed: " + e.getMessage());
        }
    }

    /**
     * Handle SUBSCRIBE command - authorize subscription
     */
    private void handleSubscribe(StompHeaderAccessor accessor) {
        String destination = accessor.getDestination();
        Principal user = accessor.getUser();
        
        if (user == null) {
            log.warn("Unauthenticated user attempting to subscribe to: {}", destination);
            throw new SecurityException("Authentication required for subscription");
        }
        
        // Authorize subscription based on destination
        if (!isSubscriptionAuthorized(destination, user)) {
            log.warn("User {} not authorized to subscribe to: {}", user.getName(), destination);
            throw new SecurityException("Not authorized to subscribe to: " + destination);
        }
        
        log.debug("User {} subscribed to: {}", user.getName(), destination);
    }

    /**
     * Handle SEND command - authorize message sending
     */
    private void handleSend(StompHeaderAccessor accessor) {
        String destination = accessor.getDestination();
        Principal user = accessor.getUser();
        
        if (user == null) {
            log.warn("Unauthenticated user attempting to send to: {}", destination);
            throw new SecurityException("Authentication required for sending messages");
        }
        
        // Authorize sending based on destination
        if (!isSendingAuthorized(destination, user)) {
            log.warn("User {} not authorized to send to: {}", user.getName(), destination);
            throw new SecurityException("Not authorized to send to: " + destination);
        }
        
        log.debug("User {} sending message to: {}", user.getName(), destination);
    }

    /**
     * Handle DISCONNECT command - cleanup
     */
    private void handleDisconnect(StompHeaderAccessor accessor) {
        Principal user = accessor.getUser();
        Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
        
        if (user != null && sessionAttributes != null) {
            String userId = (String) sessionAttributes.get("userId");
            String sessionId = (String) sessionAttributes.get("sessionId");
            long connectedAt = (Long) sessionAttributes.getOrDefault("connectedAt", 0L);
            long duration = System.currentTimeMillis() - connectedAt;
            
            log.info("WebSocket disconnected - UserId: {}, SessionId: {}, Duration: {}ms", 
                    userId, sessionId, duration);
        }
    }

    /**
     * Extract authentication token from headers
     */
    private String extractToken(StompHeaderAccessor accessor) {
        List<String> authHeaders = accessor.getNativeHeader(AUTH_HEADER);
        if (authHeaders != null && !authHeaders.isEmpty()) {
            String authHeader = authHeaders.get(0);
            if (authHeader.startsWith(BEARER_PREFIX)) {
                return authHeader.substring(BEARER_PREFIX.length());
            }
        }
        return null;
    }

    /**
     * Extract user ID from headers
     */
    private String extractUserId(StompHeaderAccessor accessor) {
        List<String> userIdHeaders = accessor.getNativeHeader(USER_ID_HEADER);
        return userIdHeaders != null && !userIdHeaders.isEmpty() ? userIdHeaders.get(0) : null;
    }

    /**
     * Extract session ID from headers
     */
    private String extractSessionId(StompHeaderAccessor accessor) {
        List<String> sessionIdHeaders = accessor.getNativeHeader(SESSION_ID_HEADER);
        return sessionIdHeaders != null && !sessionIdHeaders.isEmpty() ? sessionIdHeaders.get(0) : accessor.getSessionId();
    }

    /**
     * Authenticate user with token
     * In development mode, use mock authentication
     * In production, validate JWT token
     */
    private Authentication authenticateUser(String token, String userId) {
        try {
            // For development - mock authentication
            if (isDevelopmentMode()) {
                return createMockAuthentication(userId);
            }
            
            // For production - validate JWT token
            return validateJwtToken(token);
            
        } catch (Exception e) {
            log.error("Authentication failed for user: {}", userId, e);
            return null;
        }
    }

    /**
     * Create mock authentication for development
     */
    private Authentication createMockAuthentication(String userId) {
        if (userId == null || userId.isEmpty()) {
            userId = "dev-user";
        }
        
        List<SimpleGrantedAuthority> authorities = List.of(
                new SimpleGrantedAuthority("ROLE_USER"),
                new SimpleGrantedAuthority("ROLE_CRYPTO_VIEWER")
        );
        
        return new UsernamePasswordAuthenticationToken(userId, null, authorities);
    }

    /**
     * Validate JWT token (production implementation)
     */
    private Authentication validateJwtToken(String token) {
        // TODO: Implement JWT token validation with Keycloak
        // This would involve:
        // 1. Validate token signature
        // 2. Check token expiration
        // 3. Extract user claims
        // 4. Create Authentication object
        
        return null; // Placeholder
    }

    /**
     * Check if subscription is authorized
     */
    private boolean isSubscriptionAuthorized(String destination, Principal user) {
        if (destination == null) {
            return false;
        }
        
        // Public crypto topics - accessible to all authenticated users
        if (destination.startsWith("/topic/crypto")) {
            return true;
        }
        
        // User-specific queues - only accessible to the owner
        if (destination.startsWith("/user/queue")) {
            return destination.contains("/" + user.getName() + "/");
        }
        
        // Admin topics - require admin role
        if (destination.startsWith("/topic/admin")) {
            return hasRole(user, "ADMIN");
        }
        
        return false;
    }

    /**
     * Check if sending is authorized
     */
    private boolean isSendingAuthorized(String destination, Principal user) {
        if (destination == null) {
            return false;
        }
        
        // Application destinations - allow authenticated users
        if (destination.startsWith("/app/")) {
            return true;
        }
        
        return false;
    }

    /**
     * Check if user has specific role
     */
    private boolean hasRole(Principal user, String role) {
        if (user instanceof UsernamePasswordAuthenticationToken) {
            UsernamePasswordAuthenticationToken auth = (UsernamePasswordAuthenticationToken) user;
            return auth.getAuthorities().stream()
                    .anyMatch(authority -> authority.getAuthority().equals("ROLE_" + role));
        }
        return false;
    }

    /**
     * Check if running in development mode
     */
    private boolean isDevelopmentMode() {
        return SecurityUtils.isAuthenticated() || 
               System.getProperty("spring.profiles.active", "").contains("dev");
    }

    /**
     * Task scheduler for WebSocket operations
     */
    private org.springframework.scheduling.TaskScheduler taskScheduler() {
        org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler scheduler = 
                new org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler();
        scheduler.setPoolSize(2);
        scheduler.setThreadNamePrefix("websocket-broker-");
        scheduler.initialize();
        return scheduler;
    }
}
