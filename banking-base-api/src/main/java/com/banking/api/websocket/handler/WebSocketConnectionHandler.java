package com.banking.api.websocket.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket Connection Handler
 * Manages WebSocket connections, monitoring, and lifecycle
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Component
public class WebSocketConnectionHandler implements HandshakeInterceptor {

    private final Map<String, ConnectionInfo> activeConnections = new ConcurrentHashMap<>();
    private final AtomicInteger totalConnections = new AtomicInteger(0);
    private final AtomicInteger currentConnections = new AtomicInteger(0);

    public static class ConnectionInfo {
        private final String sessionId;
        private final String remoteAddress;
        private final long connectedAt;
        private final String userAgent;
        private volatile long lastActivity;

        public ConnectionInfo(String sessionId, String remoteAddress, String userAgent) {
            this.sessionId = sessionId;
            this.remoteAddress = remoteAddress;
            this.userAgent = userAgent;
            this.connectedAt = System.currentTimeMillis();
            this.lastActivity = connectedAt;
        }

        public String getSessionId() { return sessionId; }
        public String getRemoteAddress() { return remoteAddress; }
        public long getConnectedAt() { return connectedAt; }
        public String getUserAgent() { return userAgent; }
        public long getLastActivity() { return lastActivity; }

        public void updateActivity() {
            this.lastActivity = System.currentTimeMillis();
        }

        public long getConnectionDuration() {
            return System.currentTimeMillis() - connectedAt;
        }
    }

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) {
        
        String sessionId = generateSessionId();
        String remoteAddress = getRemoteAddress(request);
        String userAgent = request.getHeaders().getFirst("User-Agent");
        
        log.info("WebSocket handshake - SessionId: {}, RemoteAddress: {}", sessionId, remoteAddress);
        
        try {
            // Disable rate limiting and connection limits for development
            // TODO: Re-enable for production
            /*
            if (currentConnections.get() >= getMaxConnections()) {
                log.warn("WebSocket connection rejected - Max connections reached: {}", currentConnections.get());
                return false;
            }

            if (!isRateLimitPassed(remoteAddress)) {
                log.warn("WebSocket connection rejected - Rate limit exceeded for: {}", remoteAddress);
                return false;
            }
            */
            
            ConnectionInfo connectionInfo = new ConnectionInfo(sessionId, remoteAddress, userAgent);
            activeConnections.put(sessionId, connectionInfo);
            
            totalConnections.incrementAndGet();
            currentConnections.incrementAndGet();
            
            attributes.put("connectionInfo", connectionInfo);
            attributes.put("sessionId", sessionId);
            
            log.info("WebSocket handshake successful - SessionId: {}, Total connections: {}", 
                    sessionId, currentConnections.get());
            
            return true;
            
        } catch (Exception e) {
            log.error("WebSocket handshake error", e);
            return false;
        }
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        if (exception != null) {
            log.error("WebSocket handshake failed", exception);
        }
    }

    public void handleDisconnect(String sessionId) {
        ConnectionInfo connectionInfo = activeConnections.remove(sessionId);
        if (connectionInfo != null) {
            currentConnections.decrementAndGet();
            log.info("WebSocket disconnected - SessionId: {}, Duration: {}ms", 
                    sessionId, connectionInfo.getConnectionDuration());
        }
    }

    public Map<String, Object> getConnectionStats() {
        return Map.of(
                "totalConnections", totalConnections.get(),
                "currentConnections", currentConnections.get(),
                "activeConnections", activeConnections.size()
        );
    }

    private String generateSessionId() {
        return "ws-" + System.currentTimeMillis() + "-" + 
               Integer.toHexString((int) (Math.random() * 0x10000));
    }

    private String getRemoteAddress(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddress() != null ? 
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    private boolean isRateLimitPassed(String remoteAddress) {
        long connectionsFromIp = activeConnections.values().stream()
                .filter(conn -> conn.getRemoteAddress().equals(remoteAddress))
                .count();
        return connectionsFromIp < 5;
    }

    private int getMaxConnections() {
        return 1000;
    }
}
