package com.banking.api.service;

import com.banking.api.dto.BankAccountDto;
import com.banking.api.entity.BankAccount;
import com.banking.api.exception.ResourceNotFoundException;
import com.banking.api.repository.BankAccountRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * Bank Account Service
 * Handles business logic for bank account operations
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@Transactional
public class BankAccountService extends BaseService<BankAccount, BankAccountRepository> {

    private final BankAccountRepository bankAccountRepository;

    public BankAccountService(BankAccountRepository bankAccountRepository) {
        super(bankAccountRepository);
        this.bankAccountRepository = bankAccountRepository;
    }

    @Override
    protected String getEntityName() {
        return "BankAccount";
    }

    @Override
    protected void updateEntityFields(BankAccount existingEntity, BankAccount updatedEntity) {
        existingEntity.setAccountType(updatedEntity.getAccountType());
        existingEntity.setBalance(updatedEntity.getBalance());
        existingEntity.setCurrency(updatedEntity.getCurrency());
        existingEntity.setInterestRate(updatedEntity.getInterestRate());
        existingEntity.setStatus(updatedEntity.getStatus());
        existingEntity.setOpenedDate(updatedEntity.getOpenedDate());
    }

    /**
     * Find account by account number
     */
    @Transactional(readOnly = true)
    public BankAccountDto findByAccountNumber(String accountNumber) {
        log.debug("Finding account by number: {}", accountNumber);
        
        BankAccount account = bankAccountRepository.findByAccountNumber(accountNumber)
                .orElseThrow(() -> new ResourceNotFoundException("Bank account not found with number: " + accountNumber));
        
        return convertToDto(account);
    }

    /**
     * Find accounts by customer ID
     */
    @Transactional(readOnly = true)
    public List<BankAccountDto> findByCustomerId(Long customerId) {
        log.debug("Finding accounts for customer: {}", customerId);

        List<BankAccount> accounts = bankAccountRepository.findByCustomerId(customerId);
        return accounts.stream()
                .map(this::convertToDto)
                .toList();
    }

    /**
     * Find accounts by status
     */
    @Transactional(readOnly = true)
    public List<BankAccountDto> findByStatus(BankAccount.AccountStatus status) {
        log.debug("Finding accounts by status: {}", status);
        
        List<BankAccount> accounts = bankAccountRepository.findByStatus(status);
        return accounts.stream()
                .map(this::convertToDto)
                .toList();
    }

    /**
     * Find accounts by account type
     */
    @Transactional(readOnly = true)
    public List<BankAccountDto> findByAccountType(BankAccount.AccountType accountType) {
        log.debug("Finding accounts by type: {}", accountType);
        
        List<BankAccount> accounts = bankAccountRepository.findByAccountType(accountType);
        return accounts.stream()
                .map(this::convertToDto)
                .toList();
    }

    /**
     * Update account status
     */
    public BankAccountDto updateStatus(UUID accountId, BankAccount.AccountStatus status) {
        log.debug("Updating status for account: {} to {}", accountId, status);
        
        BankAccount account = findById(accountId);
        account.setStatus(status);
        
        BankAccount savedAccount = save(account);
        return convertToDto(savedAccount);
    }

    /**
     * Convert entity to DTO
     */
    private BankAccountDto convertToDto(BankAccount account) {
        return BankAccountDto.builder()
                .id(account.getId())
                .accountNumber(account.getAccountNumber())
                .customerId(account.getCustomer().getId())
                .accountType(account.getAccountType())
                .balance(account.getBalance())
                .currency(account.getCurrency())
                .interestRate(account.getInterestRate())
                .status(account.getStatus())
                .openedDate(account.getOpenedDate())
                .lastTransactionDate(account.getLastTransactionDate())
                .createdAt(account.getCreatedAt())
                .updatedAt(account.getUpdatedAt())
                .build();
    }
} 