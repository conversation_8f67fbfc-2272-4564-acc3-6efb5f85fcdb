package com.banking.api.service.crypto;

import com.banking.api.dto.crypto.CryptoPriceData;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Crypto Data Service
 * Enterprise-grade service for fetching cryptocurrency data from multiple sources
 * 
 * Features:
 * - Multiple API source support (CoinGecko, CoinMarketCap)
 * - Circuit breaker pattern
 * - Retry with exponential backoff
 * - Caching for performance
 * - Rate limiting compliance
 * - Error handling and fallback
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CryptoDataService {

    private final WebClient.Builder webClientBuilder;
    private final ObjectMapper objectMapper;

    @Value("${banking.crypto.api.coingecko.url:https://api.coingecko.com/api/v3}")
    private String coinGeckoApiUrl;

    @Value("${banking.crypto.api.coingecko.key:}")
    private String coinGeckoApiKey;

    @Value("${banking.crypto.api.coinmarketcap.url:https://pro-api.coinmarketcap.com/v1}")
    private String coinMarketCapApiUrl;

    @Value("${banking.crypto.api.coinmarketcap.key:}")
    private String coinMarketCapApiKey;

    @Value("${banking.crypto.default-symbols:BTC,ETH,ADA,DOT,LINK}")
    private String defaultSymbols;

    @Value("${banking.crypto.request-timeout:10000}")
    private int requestTimeoutMs;

    @Value("${banking.crypto.max-retries:3}")
    private int maxRetries;

    // Cache for API rate limiting
    private final Map<String, Long> lastApiCall = new ConcurrentHashMap<>();
    private final Map<String, Integer> apiCallCount = new ConcurrentHashMap<>();

    /**
     * Data source enum
     */
    public enum DataSource {
        COINGECKO("coingecko", 50), // 50 calls per minute free tier
        COINMARKETCAP("coinmarketcap", 333); // 10,000 calls per month free tier

        private final String name;
        private final int rateLimitPerMinute;

        DataSource(String name, int rateLimitPerMinute) {
            this.name = name;
            this.rateLimitPerMinute = rateLimitPerMinute;
        }

        public String getName() { return name; }
        public int getRateLimitPerMinute() { return rateLimitPerMinute; }
    }

    /**
     * Fetch crypto data for multiple symbols
     */
    public Flux<CryptoPriceData> fetchCryptoData(Set<String> symbols) {
        return fetchCryptoData(symbols, DataSource.COINGECKO);
    }

    /**
     * Fetch crypto data from specific source
     */
    public Flux<CryptoPriceData> fetchCryptoData(Set<String> symbols, DataSource source) {
        log.info("Fetching crypto data for symbols: {} from source: {}", symbols, source.getName());

        return switch (source) {
            case COINGECKO -> fetchFromCoinGecko(symbols);
            case COINMARKETCAP -> fetchFromCoinMarketCap(symbols);
        };
    }

    /**
     * Fetch data from CoinGecko API
     */
    private Flux<CryptoPriceData> fetchFromCoinGecko(Set<String> symbols) {
        if (!isRateLimitOk(DataSource.COINGECKO)) {
            log.warn("Rate limit exceeded for CoinGecko, skipping fetch");
            return Flux.empty();
        }

        String symbolsParam = String.join(",", symbols).toLowerCase();
        String url = coinGeckoApiUrl + "/simple/price";

        WebClient webClient = createWebClient(coinGeckoApiUrl);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/simple/price")
                        .queryParam("ids", symbolsParam)
                        .queryParam("vs_currencies", "usd,btc")
                        .queryParam("include_market_cap", "true")
                        .queryParam("include_24hr_vol", "true")
                        .queryParam("include_24hr_change", "true")
                        .queryParam("include_last_updated_at", "true")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMillis(requestTimeoutMs))
                .retryWhen(Retry.backoff(maxRetries, Duration.ofSeconds(1))
                        .filter(this::isRetryableException))
                .flatMapMany(this::parseCoinGeckoResponse)
                .doOnNext(data -> updateRateLimit(DataSource.COINGECKO))
                .doOnError(error -> log.error("Error fetching from CoinGecko", error))
                .onErrorResume(error -> {
                    log.warn("CoinGecko fetch failed, trying fallback source");
                    return fetchFromCoinMarketCap(symbols);
                });
    }

    /**
     * Fetch data from CoinMarketCap API
     */
    private Flux<CryptoPriceData> fetchFromCoinMarketCap(Set<String> symbols) {
        if (!isRateLimitOk(DataSource.COINMARKETCAP)) {
            log.warn("Rate limit exceeded for CoinMarketCap, skipping fetch");
            return Flux.empty();
        }

        if (coinMarketCapApiKey.isEmpty()) {
            log.warn("CoinMarketCap API key not configured");
            return Flux.empty();
        }

        String symbolsParam = String.join(",", symbols);
        WebClient webClient = createWebClient(coinMarketCapApiUrl);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/cryptocurrency/quotes/latest")
                        .queryParam("symbol", symbolsParam)
                        .queryParam("convert", "USD,BTC")
                        .build())
                .header("X-CMC_PRO_API_KEY", coinMarketCapApiKey)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMillis(requestTimeoutMs))
                .retryWhen(Retry.backoff(maxRetries, Duration.ofSeconds(1))
                        .filter(this::isRetryableException))
                .flatMapMany(this::parseCoinMarketCapResponse)
                .doOnNext(data -> updateRateLimit(DataSource.COINMARKETCAP))
                .doOnError(error -> log.error("Error fetching from CoinMarketCap", error));
    }

    /**
     * Get cached crypto data
     */
    @Cacheable(value = "crypto-data", key = "#symbols.toString()")
    public Flux<CryptoPriceData> getCachedCryptoData(Set<String> symbols) {
        return fetchCryptoData(symbols);
    }

    /**
     * Get default crypto symbols
     */
    public Set<String> getDefaultSymbols() {
        return Set.of(defaultSymbols.split(","));
    }

    /**
     * Parse CoinGecko API response
     */
    private Flux<CryptoPriceData> parseCoinGeckoResponse(String response) {
        try {
            JsonNode rootNode = objectMapper.readTree(response);
            
            return Flux.fromIterable(() -> rootNode.fields())
                    .map(entry -> {
                        String symbol = entry.getKey().toUpperCase();
                        JsonNode data = entry.getValue();
                        
                        return CryptoPriceData.builder()
                                .symbol(symbol)
                                .name(symbol) // CoinGecko simple API doesn't return full name
                                .priceUsd(getBigDecimal(data, "usd"))
                                .priceBtc(getBigDecimal(data, "btc"))
                                .marketCapUsd(getBigDecimal(data, "usd_market_cap"))
                                .volume24hUsd(getBigDecimal(data, "usd_24h_vol"))
                                .percentChange24h(getBigDecimal(data, "usd_24h_change"))
                                .source(DataSource.COINGECKO.getName())
                                .timestamp(LocalDateTime.now())
                                .lastUpdated(LocalDateTime.now())
                                .build();
                    })
                    .filter(CryptoPriceData::isValid);
                    
        } catch (Exception e) {
            log.error("Error parsing CoinGecko response", e);
            return Flux.error(e);
        }
    }

    /**
     * Parse CoinMarketCap API response
     */
    private Flux<CryptoPriceData> parseCoinMarketCapResponse(String response) {
        try {
            JsonNode rootNode = objectMapper.readTree(response);
            JsonNode dataNode = rootNode.get("data");
            
            if (dataNode == null || !dataNode.isObject()) {
                return Flux.empty();
            }
            
            return Flux.fromIterable(() -> dataNode.fields())
                    .map(entry -> {
                        JsonNode coinData = entry.getValue().get(0); // First element in array
                        JsonNode quote = coinData.get("quote").get("USD");
                        
                        return CryptoPriceData.builder()
                                .symbol(coinData.get("symbol").asText())
                                .name(coinData.get("name").asText())
                                .priceUsd(getBigDecimal(quote, "price"))
                                .marketCapUsd(getBigDecimal(quote, "market_cap"))
                                .volume24hUsd(getBigDecimal(quote, "volume_24h"))
                                .percentChange24h(getBigDecimal(quote, "percent_change_24h"))
                                .percentChange7d(getBigDecimal(quote, "percent_change_7d"))
                                .percentChange30d(getBigDecimal(quote, "percent_change_30d"))
                                .rank(coinData.get("cmc_rank").asInt())
                                .circulatingSupply(getBigDecimal(coinData, "circulating_supply"))
                                .totalSupply(getBigDecimal(coinData, "total_supply"))
                                .maxSupply(getBigDecimal(coinData, "max_supply"))
                                .source(DataSource.COINMARKETCAP.getName())
                                .timestamp(LocalDateTime.now())
                                .build();
                    })
                    .filter(CryptoPriceData::isValid);
                    
        } catch (Exception e) {
            log.error("Error parsing CoinMarketCap response", e);
            return Flux.error(e);
        }
    }

    /**
     * Create WebClient with common configuration
     */
    private WebClient createWebClient(String baseUrl) {
        return webClientBuilder
                .baseUrl(baseUrl)
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024)) // 1MB
                .build();
    }

    /**
     * Check if API rate limit is OK
     */
    private boolean isRateLimitOk(DataSource source) {
        String key = source.getName();
        long now = System.currentTimeMillis();
        long lastCall = lastApiCall.getOrDefault(key, 0L);
        
        // Reset counter every minute
        if (now - lastCall > 60000) {
            apiCallCount.put(key, 0);
            lastApiCall.put(key, now);
        }
        
        int currentCount = apiCallCount.getOrDefault(key, 0);
        return currentCount < source.getRateLimitPerMinute();
    }

    /**
     * Update rate limit counter
     */
    private void updateRateLimit(DataSource source) {
        String key = source.getName();
        apiCallCount.merge(key, 1, Integer::sum);
    }

    /**
     * Check if exception is retryable
     */
    private boolean isRetryableException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            int statusCode = ex.getStatusCode().value();
            return statusCode >= 500 || statusCode == 429; // Server errors or rate limit
        }
        return true; // Retry other exceptions
    }

    /**
     * Safely get BigDecimal from JsonNode
     */
    private BigDecimal getBigDecimal(JsonNode node, String fieldName) {
        JsonNode field = node.get(fieldName);
        if (field == null || field.isNull()) {
            return null;
        }
        try {
            return new BigDecimal(field.asText());
        } catch (NumberFormatException e) {
            log.warn("Invalid number format for field {}: {}", fieldName, field.asText());
            return null;
        }
    }
}
