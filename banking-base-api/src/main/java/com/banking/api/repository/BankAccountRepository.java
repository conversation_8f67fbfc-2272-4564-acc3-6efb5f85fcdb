package com.banking.api.repository;

import com.banking.api.entity.BankAccount;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Bank Account Repository
 * Handles data access for BankAccount entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface BankAccountRepository extends BaseRepository<BankAccount> {

    /**
     * Find account by account number
     */
    Optional<BankAccount> findByAccountNumber(String accountNumber);

    /**
     * Find accounts by customer ID
     */
    List<BankAccount> findByCustomerId(Long customerId);

    /**
     * Find accounts by status
     */
    List<BankAccount> findByStatus(BankAccount.AccountStatus status);

    /**
     * Find accounts by account type
     */
    List<BankAccount> findByAccountType(BankAccount.AccountType accountType);

    /**
     * Find accounts by customer ID and status
     */
    List<BankAccount> findByCustomerIdAndStatus(Long customerId, BankAccount.AccountStatus status);

    /**
     * Check if account exists by account number
     */
    boolean existsByAccountNumber(String accountNumber);

    /**
     * Find accounts with balance greater than specified amount
     */
    List<BankAccount> findByBalanceGreaterThan(java.math.BigDecimal amount);

    /**
     * Find accounts by currency
     */
    List<BankAccount> findByCurrency(String currency);
} 