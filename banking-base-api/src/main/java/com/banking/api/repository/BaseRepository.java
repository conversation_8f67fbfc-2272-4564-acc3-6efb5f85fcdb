package com.banking.api.repository;

import com.banking.api.entity.BaseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Base Repository Interface
 * Provides common database operations for all entities
 * 
 * @param <T> Entity type extending BaseEntity
 * <AUTHOR> Team
 * @version 1.0.0
 */
@NoRepositoryBean
public interface BaseRepository<T extends BaseEntity> extends JpaRepository<T, UUID> {

    /**
     * Find entity by ID excluding soft deleted
     * 
     * @param id Entity ID
     * @return Optional entity
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.id = :id AND e.isDeleted = false")
    Optional<T> findByIdAndIsDeletedFalse(@Param("id") UUID id);

    /**
     * Find all entities excluding soft deleted
     * 
     * @return List of entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.isDeleted = false ORDER BY e.createdAt DESC")
    List<T> findAllByIsDeletedFalse();

    /**
     * Find all entities excluding soft deleted with pagination
     * 
     * @param pageable Pagination information
     * @return Page of entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.isDeleted = false")
    Page<T> findAllByIsDeletedFalse(Pageable pageable);

    /**
     * Find entities created by specific user
     * 
     * @param createdBy User who created the entities
     * @return List of entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.createdBy = :createdBy AND e.isDeleted = false ORDER BY e.createdAt DESC")
    List<T> findByCreatedByAndIsDeletedFalse(@Param("createdBy") String createdBy);

    /**
     * Find entities created by specific user with pagination
     * 
     * @param createdBy User who created the entities
     * @param pageable Pagination information
     * @return Page of entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.createdBy = :createdBy AND e.isDeleted = false")
    Page<T> findByCreatedByAndIsDeletedFalse(@Param("createdBy") String createdBy, Pageable pageable);

    /**
     * Find entities updated by specific user
     * 
     * @param updatedBy User who updated the entities
     * @return List of entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.updatedBy = :updatedBy AND e.isDeleted = false ORDER BY e.updatedAt DESC")
    List<T> findByUpdatedByAndIsDeletedFalse(@Param("updatedBy") String updatedBy);

    /**
     * Find entities created within date range
     * 
     * @param startDate Start date
     * @param endDate End date
     * @return List of entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.createdAt BETWEEN :startDate AND :endDate AND e.isDeleted = false ORDER BY e.createdAt DESC")
    List<T> findByCreatedAtBetweenAndIsDeletedFalse(@Param("startDate") LocalDateTime startDate, 
                                                    @Param("endDate") LocalDateTime endDate);

    /**
     * Find entities created within date range with pagination
     * 
     * @param startDate Start date
     * @param endDate End date
     * @param pageable Pagination information
     * @return Page of entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.createdAt BETWEEN :startDate AND :endDate AND e.isDeleted = false")
    Page<T> findByCreatedAtBetweenAndIsDeletedFalse(@Param("startDate") LocalDateTime startDate, 
                                                    @Param("endDate") LocalDateTime endDate, 
                                                    Pageable pageable);

    /**
     * Find entities updated within date range
     * 
     * @param startDate Start date
     * @param endDate End date
     * @return List of entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.updatedAt BETWEEN :startDate AND :endDate AND e.isDeleted = false ORDER BY e.updatedAt DESC")
    List<T> findByUpdatedAtBetweenAndIsDeletedFalse(@Param("startDate") LocalDateTime startDate, 
                                                    @Param("endDate") LocalDateTime endDate);

    /**
     * Count entities excluding soft deleted
     * 
     * @return Count of entities
     */
    @Query("SELECT COUNT(e) FROM #{#entityName} e WHERE e.isDeleted = false")
    long countByIsDeletedFalse();

    /**
     * Count entities created by specific user
     * 
     * @param createdBy User who created the entities
     * @return Count of entities
     */
    @Query("SELECT COUNT(e) FROM #{#entityName} e WHERE e.createdBy = :createdBy AND e.isDeleted = false")
    long countByCreatedByAndIsDeletedFalse(@Param("createdBy") String createdBy);

    /**
     * Count entities created within date range
     * 
     * @param startDate Start date
     * @param endDate End date
     * @return Count of entities
     */
    @Query("SELECT COUNT(e) FROM #{#entityName} e WHERE e.createdAt BETWEEN :startDate AND :endDate AND e.isDeleted = false")
    long countByCreatedAtBetweenAndIsDeletedFalse(@Param("startDate") LocalDateTime startDate, 
                                                  @Param("endDate") LocalDateTime endDate);

    /**
     * Check if entity exists by ID excluding soft deleted
     * 
     * @param id Entity ID
     * @return true if entity exists
     */
    @Query("SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END FROM #{#entityName} e WHERE e.id = :id AND e.isDeleted = false")
    boolean existsByIdAndIsDeletedFalse(@Param("id") UUID id);

    /**
     * Find soft deleted entities
     * 
     * @return List of soft deleted entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.isDeleted = true ORDER BY e.updatedAt DESC")
    List<T> findAllDeleted();

    /**
     * Find soft deleted entities with pagination
     * 
     * @param pageable Pagination information
     * @return Page of soft deleted entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.isDeleted = true")
    Page<T> findAllDeleted(Pageable pageable);

    /**
     * Find entities deleted by specific user
     * 
     * @param deletedBy User who deleted the entities
     * @return List of deleted entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.updatedBy = :deletedBy AND e.isDeleted = true ORDER BY e.updatedAt DESC")
    List<T> findByDeletedByAndIsDeletedTrue(@Param("deletedBy") String deletedBy);

    /**
     * Find entities deleted within date range
     * 
     * @param startDate Start date
     * @param endDate End date
     * @return List of deleted entities
     */
    @Query("SELECT e FROM #{#entityName} e WHERE e.updatedAt BETWEEN :startDate AND :endDate AND e.isDeleted = true ORDER BY e.updatedAt DESC")
    List<T> findByDeletedAtBetweenAndIsDeletedTrue(@Param("startDate") LocalDateTime startDate, 
                                                   @Param("endDate") LocalDateTime endDate);

    /**
     * Restore soft deleted entity
     * 
     * @param id Entity ID
     * @return Number of updated records
     */
    @Query("UPDATE #{#entityName} e SET e.isDeleted = false WHERE e.id = :id AND e.isDeleted = true")
    int restoreById(@Param("id") UUID id);

    /**
     * Permanently delete old soft deleted entities
     * 
     * @param cutoffDate Cutoff date for permanent deletion
     * @return Number of permanently deleted records
     */
    @Query("DELETE FROM #{#entityName} e WHERE e.isDeleted = true AND e.updatedAt < :cutoffDate")
    int permanentlyDeleteOldSoftDeletedEntities(@Param("cutoffDate") LocalDateTime cutoffDate);
}
